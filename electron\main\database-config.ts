import { Sequelize } from 'sequelize'
import { join } from 'path'
import { app } from 'electron'
import { SQLiteAdapter } from './sqlite-adapter'

// 数据库类型
export type DatabaseType = 'mysql' | 'sqlite'

// 数据库配置接口
export interface DatabaseConfig {
  type: DatabaseType
  mysql: {
    host: string
    port: number
    username: string
    password: string
    database: string
  }
  sqlite: {
    storage: string
  }
}

// 默认配置
const DEFAULT_CONFIG: DatabaseConfig = {
  type: 'sqlite', // 默认使用SQLite（轻量级，无需外部数据库）
  mysql: {
    host: '127.0.0.1',
    port: 3306,
    username: 'root',
    password: 'zy123good',
    database: 'hiltonmarket'
  },
  sqlite: {
    storage: join(app.getPath('userData'), 'hiltonmarket.db')
  }
}

// 当前配置
let currentConfig: DatabaseConfig = { ...DEFAULT_CONFIG }

// SQLite适配器实例
let sqliteAdapter: SQLiteAdapter | null = null

// 获取当前配置
export const getDatabaseConfig = (): DatabaseConfig => {
  return currentConfig
}

// 设置数据库类型
export const setDatabaseType = (type: DatabaseType): void => {
  currentConfig.type = type
  console.log(`🔄 数据库类型切换为: ${type.toUpperCase()}`)
}

// 更新MySQL配置
export const updateMySQLConfig = (config: Partial<DatabaseConfig['mysql']>): void => {
  currentConfig.mysql = { ...currentConfig.mysql, ...config }
  console.log('🔧 MySQL配置已更新')
}

// 更新SQLite配置
export const updateSQLiteConfig = (config: Partial<DatabaseConfig['sqlite']>): void => {
  currentConfig.sqlite = { ...currentConfig.sqlite, ...config }
  console.log('🔧 SQLite配置已更新')
}

// 创建Sequelize实例
export const createSequelizeInstance = (): Sequelize => {
  const config = getDatabaseConfig()

  if (config.type === 'mysql') {
    console.log('🔗 连接MySQL数据库...')
    return new Sequelize({
      dialect: 'mysql',
      host: config.mysql.host,
      port: config.mysql.port,
      username: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database,
      logging: false,
      pool: {
        max: 3,
        min: 1,
        acquire: 10000,
        idle: 5000
      },
      dialectOptions: {
        connectTimeout: 5000,
        acquireTimeout: 5000,
        timeout: 5000
      }
    })
  } else {
    console.log('🔗 使用SQLite数据库（sql.js）...')
    // 对于SQLite，我们不创建Sequelize实例，直接返回null
    // 实际的数据库操作将通过SQLiteAdapter处理
    throw new Error('SQLite模式下不使用Sequelize，请使用SQLiteAdapter')
  }
}

// 获取SQLite适配器
export const getSQLiteAdapter = (): SQLiteAdapter | null => {
  return sqliteAdapter
}

// 初始化SQLite适配器
export const initSQLiteAdapter = async (): Promise<void> => {
  const config = getDatabaseConfig()
  if (config.type === 'sqlite') {
    sqliteAdapter = new SQLiteAdapter(config.sqlite.storage)
    await sqliteAdapter.init()
    await sqliteAdapter.initializeData()
  }
}

// 关闭SQLite适配器
export const closeSQLiteAdapter = (): void => {
  if (sqliteAdapter) {
    sqliteAdapter.close()
    sqliteAdapter = null
  }
}

// 获取数据库信息
export const getDatabaseInfo = () => {
  const config = getDatabaseConfig()

  if (config.type === 'mysql') {
    return {
      type: 'MySQL',
      connection: `${config.mysql.host}:${config.mysql.port}/${config.mysql.database}`,
      description: 'MySQL数据库 - 适合生产环境'
    }
  } else {
    return {
      type: 'SQLite',
      connection: config.sqlite.storage,
      description: 'SQLite文件数据库 - 适合开发和小型应用'
    }
  }
}

// 从环境变量读取配置
export const loadConfigFromEnv = (): void => {
  // 从环境变量读取数据库类型
  const dbType = process.env.DB_TYPE as DatabaseType
  if (dbType && ['mysql', 'sqlite'].includes(dbType)) {
    setDatabaseType(dbType)
  }

  // 从环境变量读取MySQL配置
  if (process.env.DB_HOST) {
    updateMySQLConfig({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT ? parseInt(process.env.DB_PORT) : 3306,
      username: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_DATABASE || 'hiltonmarket'
    })
  }

  // 从环境变量读取SQLite配置
  if (process.env.SQLITE_STORAGE) {
    updateSQLiteConfig({
      storage: process.env.SQLITE_STORAGE
    })
  }
}

// 验证配置
export const validateConfig = async (sequelize: Sequelize): Promise<boolean> => {
  try {
    await sequelize.authenticate()
    const info = getDatabaseInfo()
    console.log(`✅ ${info.type}数据库连接成功`)
    console.log(`📍 连接地址: ${info.connection}`)
    return true
  } catch (error) {
    const info = getDatabaseInfo()
    console.error(`❌ ${info.type}数据库连接失败:`, error)
    return false
  }
}

// 导出配置常量
export const DB_TYPES = {
  MYSQL: 'mysql' as const,
  SQLITE: 'sqlite' as const
}
