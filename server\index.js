const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8000;

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 模拟数据
const hotelData = {
  basicInfo: [
    { label: '计划制定人', value: 'Alice', note: '用户手填' },
    { label: '酒店In Code', value: 'AOGCN', note: 'incode预处理数据' },
    { label: '酒店名称', value: '九寨沟康莱德酒店', note: '预填与incode匹配' },
    { label: '酒店所在区域', value: '西区', note: '预填与incode匹配' },
    { label: '总经理', value: 'A', note: '用户手填' },
    { label: '商务总监', value: 'B', note: '用户手填' },
    { label: '市场总监', value: 'C', note: '用户手填' },
    { label: 'MECC 联系人', value: 'D', note: '用户手填' }
  ],
  budgetInfo: [
    { label: '酒店本地活动预算', value: '¥620,125.00' },
    { label: '集团市场共享费（Co-op Fund）', value: '¥349,561.33' },
    { label: 'PMP', value: '¥60,000.00' },
    { label: '总预算', value: '¥1,029,686.33' }
  ],
  uspData: [
    {
      rooms: ['家庭房', '景观房', '独特风格房型', '亲子房'],
      dining: ['免费早餐', '餐厅拥有佳景', '国际美食'],
      meeting: ['1000平米无柱宴会厅', '40平米高清LED', '10种会议室组合'],
      services: ['室外泳池/儿童泳池', 'SPA', '运动中心、健身房']
    }
  ]
};

// 用户数据
const users = [
  { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin' },
  { id: 2, username: 'user', email: '<EMAIL>', role: 'user' }
];

// API路由

// 健康检查
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'Node.js API Server is running',
    timestamp: new Date().toISOString()
  });
});

// 酒店信息相关API
app.get('/hotel/info', (req, res) => {
  res.json({
    code: 200,
    message: 'success',
    data: hotelData
  });
});

app.get('/hotel/basic', (req, res) => {
  res.json({
    code: 200,
    message: 'success',
    data: hotelData.basicInfo
  });
});

app.get('/hotel/budget', (req, res) => {
  res.json({
    code: 200,
    message: 'success',
    data: hotelData.budgetInfo
  });
});

app.get('/hotel/usp', (req, res) => {
  res.json({
    code: 200,
    message: 'success',
    data: hotelData.uspData
  });
});

// 更新酒店基本信息
app.put('/hotel/basic', (req, res) => {
  const { basicInfo } = req.body;
  if (basicInfo && Array.isArray(basicInfo)) {
    hotelData.basicInfo = basicInfo;
    res.json({
      code: 200,
      message: '酒店基本信息更新成功',
      data: hotelData.basicInfo
    });
  } else {
    res.status(400).json({
      code: 400,
      message: '无效的数据格式'
    });
  }
});

// 用户相关API
app.get('/users', (req, res) => {
  const { page = 1, limit = 10, search = '' } = req.query;
  
  let filteredUsers = users;
  if (search) {
    filteredUsers = users.filter(user => 
      user.username.includes(search) || user.email.includes(search)
    );
  }
  
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
  
  res.json({
    code: 200,
    message: 'success',
    data: {
      users: paginatedUsers,
      total: filteredUsers.length,
      page: parseInt(page),
      limit: parseInt(limit)
    }
  });
});

app.get('/users/:id', (req, res) => {
  const { id } = req.params;
  const user = users.find(u => u.id === parseInt(id));
  
  if (user) {
    res.json({
      code: 200,
      message: 'success',
      data: user
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '用户不存在'
    });
  }
});

// 创建新用户
app.post('/users', (req, res) => {
  const { username, email, role = 'user' } = req.body;
  
  if (!username || !email) {
    return res.status(400).json({
      code: 400,
      message: '用户名和邮箱是必填项'
    });
  }
  
  const newUser = {
    id: users.length + 1,
    username,
    email,
    role
  };
  
  users.push(newUser);
  
  res.status(201).json({
    code: 201,
    message: '用户创建成功',
    data: newUser
  });
});

// 更新用户
app.put('/users/:id', (req, res) => {
  const { id } = req.params;
  const { username, email, role } = req.body;
  
  const userIndex = users.findIndex(u => u.id === parseInt(id));
  
  if (userIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '用户不存在'
    });
  }
  
  if (username) users[userIndex].username = username;
  if (email) users[userIndex].email = email;
  if (role) users[userIndex].role = role;
  
  res.json({
    code: 200,
    message: '用户更新成功',
    data: users[userIndex]
  });
});

// 删除用户
app.delete('/users/:id', (req, res) => {
  const { id } = req.params;
  const userIndex = users.findIndex(u => u.id === parseInt(id));
  
  if (userIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '用户不存在'
    });
  }
  
  const deletedUser = users.splice(userIndex, 1)[0];
  
  res.json({
    code: 200,
    message: '用户删除成功',
    data: deletedUser
  });
});

// 通用错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Node.js API Server is running on http://localhost:${PORT}`);
  console.log(`📋 API Documentation:`);
  console.log(`   GET  /health           - 健康检查`);
  console.log(`   GET  /hotel/info       - 获取酒店完整信息`);
  console.log(`   GET  /hotel/basic      - 获取酒店基本信息`);
  console.log(`   GET  /hotel/budget     - 获取酒店预算信息`);
  console.log(`   GET  /hotel/usp        - 获取酒店USP信息`);
  console.log(`   PUT  /hotel/basic      - 更新酒店基本信息`);
  console.log(`   GET  /users            - 获取用户列表`);
  console.log(`   GET  /users/:id        - 获取单个用户`);
  console.log(`   POST /users            - 创建用户`);
  console.log(`   PUT  /users/:id        - 更新用户`);
  console.log(`   DELETE /users/:id      - 删除用户`);
});
