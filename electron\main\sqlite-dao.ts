import { getSQLiteAdapter } from './database-config'

// SQLite数据访问对象
export class SQLiteDAO {
  private get adapter() {
    const adapter = getSQLiteAdapter()
    if (!adapter) {
      throw new Error('SQLite适配器未初始化')
    }
    return adapter
  }

  // 用户相关操作
  async getUsers(options: { limit?: number; offset?: number; search?: string } = {}) {
    const { limit = 10, offset = 0, search = '' } = options

    let sql = 'SELECT * FROM users'
    let countSql = 'SELECT COUNT(*) as count FROM users'
    const params: any[] = []

    if (search) {
      const searchCondition = ' WHERE username LIKE ? OR email LIKE ?'
      sql += searchCondition
      countSql += searchCondition
      params.push(`%${search}%`, `%${search}%`)
    }

    sql += ' ORDER BY id ASC LIMIT ? OFFSET ?'
    params.push(limit, offset)

    const users = this.adapter.all(sql, params)
    const countResult = this.adapter.get(countSql, search ? [`%${search}%`, `%${search}%`] : [])

    return {
      users,
      total: countResult?.count || 0
    }
  }

  async getUserById(id: number) {
    return this.adapter.get('SELECT * FROM users WHERE id = ?', [id])
  }

  async createUser(userData: { username: string; email: string; role?: string }) {
    const { username, email, role = 'user' } = userData
    const result = this.adapter.run(
      'INSERT INTO users (username, email, role) VALUES (?, ?, ?)',
      [username, email, role]
    )
    return this.getUserById(result.lastID)
  }

  async updateUser(id: number, userData: Partial<{ username: string; email: string; role: string }>) {
    const fields: string[] = []
    const params: any[] = []

    if (userData.username) {
      fields.push('username = ?')
      params.push(userData.username)
    }
    if (userData.email) {
      fields.push('email = ?')
      params.push(userData.email)
    }
    if (userData.role) {
      fields.push('role = ?')
      params.push(userData.role)
    }

    if (fields.length === 0) {
      return this.getUserById(id)
    }

    fields.push('updatedAt = CURRENT_TIMESTAMP')
    params.push(id)

    this.adapter.run(
      `UPDATE users SET ${fields.join(', ')} WHERE id = ?`,
      params
    )

    return this.getUserById(id)
  }

  async deleteUser(id: number) {
    const user = this.getUserById(id)
    this.adapter.run('DELETE FROM users WHERE id = ?', [id])
    return user
  }

  // 酒店基本信息操作
  async getHotelBasicInfo() {
    return this.adapter.all('SELECT * FROM hotel_basic_info ORDER BY id ASC')
  }

  async updateHotelBasicInfo(basicInfo: Array<{ label: string; value: string; note?: string }>) {
    // 删除现有数据
    this.adapter.run('DELETE FROM hotel_basic_info')

    // 插入新数据
    for (const item of basicInfo) {
      this.adapter.run(
        'INSERT INTO hotel_basic_info (label, value, note) VALUES (?, ?, ?)',
        [item.label, item.value, item.note || null]
      )
    }

    return this.getHotelBasicInfo()
  }

  // 酒店预算信息操作
  async getHotelBudgetInfo() {
    return this.adapter.all('SELECT * FROM hotel_budget_info ORDER BY id ASC')
  }

  async updateHotelBudgetInfo(budgetInfo: Array<{ label: string; value: string }>) {
    // 删除现有数据
    this.adapter.run('DELETE FROM hotel_budget_info')

    // 插入新数据
    for (const item of budgetInfo) {
      this.adapter.run(
        'INSERT INTO hotel_budget_info (label, value) VALUES (?, ?)',
        [item.label, item.value]
      )
    }

    return this.getHotelBudgetInfo()
  }

  // 酒店USP信息操作
  async getHotelUspInfo() {
    const results = this.adapter.all('SELECT * FROM hotel_usp_info ORDER BY id ASC')
    return results.map(item => ({
      ...item,
      items: JSON.parse(item.items)
    }))
  }

  async updateHotelUspInfo(uspData: Array<any>) {
    // 删除现有数据
    this.adapter.run('DELETE FROM hotel_usp_info')

    // 插入新数据
    if (uspData.length > 0) {
      const usp = uspData[0]
      for (const [category, items] of Object.entries(usp)) {
        this.adapter.run(
          'INSERT INTO hotel_usp_info (category, items) VALUES (?, ?)',
          [category, JSON.stringify(items)]
        )
      }
    }

    return this.getHotelUspInfo()
  }

  // 获取完整酒店信息
  async getHotelInfo() {
    const basicInfo = await this.getHotelBasicInfo()
    const budgetInfo = await this.getHotelBudgetInfo()
    const uspData = await this.getHotelUspInfo()

    // 转换USP数据格式
    const uspFormatted = uspData.reduce((acc: any, item: any) => {
      acc[item.category] = item.items
      return acc
    }, {})

    return {
      basicInfo,
      budgetInfo,
      uspData: [uspFormatted]
    }
  }

  // 检查表是否存在
  async checkTablesExist(): Promise<boolean> {
    const tables = this.adapter.all(`
      SELECT name FROM sqlite_master
      WHERE type = 'table'
      AND name IN ('users', 'hotel_basic_info', 'hotel_budget_info', 'hotel_usp_info')
    `)
    return tables.length === 4
  }

  // 检查是否需要初始化数据
  async checkNeedsInitialization(): Promise<boolean> {
    const userCount = this.adapter.get('SELECT COUNT(*) as count FROM users')?.count || 0
    return userCount === 0
  }
}

// 导出单例实例
export const sqliteDAO = new SQLiteDAO()
