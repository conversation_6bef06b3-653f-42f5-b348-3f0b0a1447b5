<template>
  <div style="padding: 20px;">
    <h1>Element Plus 测试页面</h1>
    
    <!-- 基本组件测试 -->
    <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h2>基本组件测试</h2>
      
      <el-button type="primary">主要按钮</el-button>
      <el-button type="success">成功按钮</el-button>
      <el-button type="warning">警告按钮</el-button>
      
      <div style="margin: 20px 0;">
        <el-input placeholder="请输入内容" style="width: 300px;" />
      </div>
      
      <el-tag type="success">成功标签</el-tag>
      <el-tag type="info">信息标签</el-tag>
    </div>

    <!-- 表单测试 -->
    <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h2>表单测试</h2>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="用户名">
          <el-input v-model="testForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="邮箱">
          <el-input v-model="testForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="testForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据显示 -->
    <div style="background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h2>表单数据</h2>
      <pre>{{ JSON.stringify(testForm, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

const testForm = reactive({
  username: '',
  email: '',
  description: ''
})

const submitForm = () => {
  console.log('提交表单:', testForm)
  ElMessage.success('表单提交成功！')
}

const resetForm = () => {
  testForm.username = ''
  testForm.email = ''
  testForm.description = ''
  ElMessage.info('表单已重置')
}
</script>

<style scoped>
h1, h2 {
  color: #333;
}
</style>
