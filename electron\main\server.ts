import express from 'express'
import cors from 'cors'
import { app as electronApp } from 'electron'
import { join } from 'path'
import { initDatabase, closeDatabase, User, HotelBasicInfo, HotelBudgetInfo, HotelUspInfo, switchDatabase } from './database'
import { getDatabaseInfo, setDatabaseType, getDatabaseConfig } from './database-config'
import { sqliteDAO } from './sqlite-dao'
import { Op } from 'sequelize'

let server: any = null
let serverPort = 8000

// 数据库操作统一接口
const dbOperations = {
  // 用户操作
  async getUsers(options: { page: number; limit: number; search: string }) {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      const { page, limit, search } = options
      const offset = (page - 1) * limit

      const whereCondition: any = {}
      if (search) {
        whereCondition[Op.or] = [
          { username: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } }
        ]
      }

      const { count, rows } = await User.findAndCountAll({
        where: whereCondition,
        limit,
        offset,
        order: [['id', 'ASC']],
        attributes: ['id', 'username', 'email', 'role', 'createdAt', 'updatedAt']
      })

      return { users: rows.map(user => user.toJSON()), total: count }
    } else {
      return await sqliteDAO.getUsers({
        limit: options.limit,
        offset: (options.page - 1) * options.limit,
        search: options.search
      })
    }
  },

  async getUserById(id: number) {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      const user = await User.findByPk(id, {
        attributes: ['id', 'username', 'email', 'role', 'createdAt', 'updatedAt']
      })
      return user ? user.toJSON() : null
    } else {
      return await sqliteDAO.getUserById(id)
    }
  },

  async createUser(userData: { username: string; email: string; role?: string }) {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      const newUser = await User.create(userData)
      return newUser.toJSON()
    } else {
      return await sqliteDAO.createUser(userData)
    }
  },

  async updateUser(id: number, userData: Partial<{ username: string; email: string; role: string }>) {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      const user = await User.findByPk(id)
      if (!user) return null

      await user.update(userData)
      return user.toJSON()
    } else {
      return await sqliteDAO.updateUser(id, userData)
    }
  },

  async deleteUser(id: number) {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      const user = await User.findByPk(id)
      if (!user) return null

      const userData = user.toJSON()
      await user.destroy()
      return userData
    } else {
      return await sqliteDAO.deleteUser(id)
    }
  },

  // 酒店信息操作
  async getHotelInfo() {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      const basicInfo = await HotelBasicInfo.findAll({
        attributes: ['label', 'value', 'note'],
        order: [['id', 'ASC']]
      })

      const budgetInfo = await HotelBudgetInfo.findAll({
        attributes: ['label', 'value'],
        order: [['id', 'ASC']]
      })

      const uspData = await HotelUspInfo.findAll({
        attributes: ['category', 'items'],
        order: [['id', 'ASC']]
      })

      const uspFormatted = uspData.reduce((acc: any, item: any) => {
        acc[item.category] = item.items
        return acc
      }, {})

      return {
        basicInfo: basicInfo.map(item => item.toJSON()),
        budgetInfo: budgetInfo.map(item => item.toJSON()),
        uspData: [uspFormatted]
      }
    } else {
      return await sqliteDAO.getHotelInfo()
    }
  },

  async getHotelBasicInfo() {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      const basicInfo = await HotelBasicInfo.findAll({
        attributes: ['label', 'value', 'note'],
        order: [['id', 'ASC']]
      })
      return basicInfo.map(item => item.toJSON())
    } else {
      return await sqliteDAO.getHotelBasicInfo()
    }
  },

  async updateHotelBasicInfo(basicInfo: Array<{ label: string; value: string; note?: string }>) {
    const config = getDatabaseConfig()
    if (config.type === 'mysql') {
      await HotelBasicInfo.destroy({ where: {} })
      const newBasicInfo = await HotelBasicInfo.bulkCreate(basicInfo)
      return newBasicInfo.map(item => item.toJSON())
    } else {
      return await sqliteDAO.updateHotelBasicInfo(basicInfo)
    }
  }
}

// 查找可用端口
const findAvailablePort = (startPort: number): Promise<number> => {
  return new Promise((resolve) => {
    const net = require('net')
    const server = net.createServer()

    server.listen(startPort, () => {
      const port = server.address()?.port
      server.close(() => {
        resolve(port || startPort)
      })
    })

    server.on('error', () => {
      findAvailablePort(startPort + 1).then(resolve)
    })
  })
}

export const startApiServer = async (): Promise<number> => {
  try {
    // 初始化数据库
    await initDatabase()

    // 查找可用端口
    serverPort = await findAvailablePort(8000)

    const app = express()

    // 中间件配置
    app.use(cors())
    app.use(express.json())
    app.use(express.urlencoded({ extended: true }))

    // 静态文件服务
    if (electronApp.isPackaged) {
      const uploadsPath = join(process.resourcesPath, 'uploads')
      app.use('/uploads', express.static(uploadsPath))
    } else {
      app.use('/uploads', express.static(join(__dirname, '../../server/uploads')))
    }

    // API路由

    // 健康检查
    app.get('/health', (req, res) => {
      const dbInfo = getDatabaseInfo()
      res.json({
        code: 0,
        message: 'success',
        data: {
          status: 'OK',
          message: 'Electron API Server is running',
          timestamp: new Date().toISOString(),
          port: serverPort,
          database: {
            type: dbInfo.type,
            connection: dbInfo.connection,
            description: dbInfo.description
          }
        }
      })
    })

    // 数据库管理API
    app.get('/database/info', (req, res) => {
      try {
        const dbInfo = getDatabaseInfo()
        res.json({
          code: 0,
          message: 'success',
          data: dbInfo
        })
      } catch (error) {
        console.error('获取数据库信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '获取数据库信息失败'
        })
      }
    })

    app.post('/database/switch', async (req, res) => {
      try {
        const { type } = req.body

        if (!type || !['mysql', 'sqlite'].includes(type)) {
          return res.status(400).json({
            code: 400,
            message: '无效的数据库类型，支持: mysql, sqlite'
          })
        }

        await switchDatabase(type)
        const dbInfo = getDatabaseInfo()

        res.json({
          code: 0,
          message: `成功切换到 ${type.toUpperCase()} 数据库`,
          data: dbInfo
        })
      } catch (error) {
        console.error('切换数据库失败:', error)
        res.status(500).json({
          code: 500,
          message: '切换数据库失败: ' + (error as Error).message
        })
      }
    })

    // 分析页面API
    app.get('/mock/analysis/total', (req, res) => {
      res.json({
        code: 0,
        data: {
          users: 102400,
          messages: 81212,
          moneys: 9280,
          shoppings: 13600
        }
      })
    })

    app.get('/mock/analysis/userAccessSource', (req, res) => {
      res.json({
        code: 0,
        data: [
          { value: 1000, name: 'analysis.directAccess' },
          { value: 310, name: 'analysis.mailMarketing' },
          { value: 234, name: 'analysis.allianceAdvertising' },
          { value: 135, name: 'analysis.videoAdvertising' },
          { value: 1548, name: 'analysis.searchEngines' }
        ]
      })
    })

    app.get('/mock/analysis/weeklyUserActivity', (req, res) => {
      res.json({
        code: 0,
        data: [
          { value: 13253, name: 'analysis.monday' },
          { value: 34235, name: 'analysis.tuesday' },
          { value: 26321, name: 'analysis.wednesday' },
          { value: 12340, name: 'analysis.thursday' },
          { value: 24643, name: 'analysis.friday' },
          { value: 1322, name: 'analysis.saturday' },
          { value: 1324, name: 'analysis.sunday' }
        ]
      })
    })

    app.get('/mock/analysis/monthlySales', (req, res) => {
      res.json({
        code: 0,
        data: [
          { estimate: 100, actual: 120, name: 'analysis.january' },
          { estimate: 120, actual: 82, name: 'analysis.february' },
          { estimate: 161, actual: 91, name: 'analysis.march' },
          { estimate: 134, actual: 154, name: 'analysis.april' },
          { estimate: 105, actual: 162, name: 'analysis.may' },
          { estimate: 160, actual: 140, name: 'analysis.june' },
          { estimate: 165, actual: 145, name: 'analysis.july' },
          { estimate: 114, actual: 250, name: 'analysis.august' },
          { estimate: 163, actual: 134, name: 'analysis.september' },
          { estimate: 185, actual: 56, name: 'analysis.october' },
          { estimate: 118, actual: 99, name: 'analysis.november' },
          { estimate: 123, actual: 123, name: 'analysis.december' }
        ]
      })
    })

    // 工作台API
    app.get('/mock/workplace/total', (req, res) => {
      res.json({
        code: 0,
        data: {
          project: 40,
          access: 2340,
          todo: 10
        }
      })
    })

    app.get('/mock/workplace/project', (req, res) => {
      res.json({
        code: 0,
        data: [
          {
            name: 'Github',
            icon: 'akar-icons:github-fill',
            message: 'workplace.introduction',
            personal: 'Archer',
            time: new Date()
          },
          {
            name: 'Vue',
            icon: 'logos:vue',
            message: 'workplace.introduction',
            personal: 'Archer',
            time: new Date()
          },
          {
            name: 'Angular',
            icon: 'logos:angular-icon',
            message: 'workplace.introduction',
            personal: 'Archer',
            time: new Date()
          },
          {
            name: 'React',
            icon: 'logos:react',
            message: 'workplace.introduction',
            personal: 'Archer',
            time: new Date()
          },
          {
            name: 'Webpack',
            icon: 'logos:webpack',
            message: 'workplace.introduction',
            personal: 'Archer',
            time: new Date()
          },
          {
            name: 'Vite',
            icon: 'vscode-icons:file-type-vite',
            message: 'workplace.introduction',
            personal: 'Archer',
            time: new Date()
          }
        ]
      })
    })

    app.get('/mock/workplace/dynamic', (req, res) => {
      res.json({
        code: 0,
        data: [
          {
            keys: ['workplace.push', 'Github'],
            time: new Date()
          },
          {
            keys: ['workplace.push', 'Github'],
            time: new Date()
          },
          {
            keys: ['workplace.push', 'Github'],
            time: new Date()
          },
          {
            keys: ['workplace.push', 'Github'],
            time: new Date()
          },
          {
            keys: ['workplace.push', 'Github'],
            time: new Date()
          },
          {
            keys: ['workplace.push', 'Github'],
            time: new Date()
          }
        ]
      })
    })

    app.get('/mock/workplace/team', (req, res) => {
      res.json({
        code: 0,
        data: [
          {
            name: 'Github',
            icon: 'akar-icons:github-fill'
          },
          {
            name: 'Vue',
            icon: 'logos:vue'
          },
          {
            name: 'Angular',
            icon: 'logos:angular-icon'
          },
          {
            name: 'React',
            icon: 'logos:react'
          },
          {
            name: 'Webpack',
            icon: 'logos:webpack'
          },
          {
            name: 'Vite',
            icon: 'vscode-icons:file-type-vite'
          }
        ]
      })
    })

    app.get('/mock/workplace/radar', (req, res) => {
      res.json({
        code: 0,
        data: [
          { name: 'workplace.quote', max: 65, personal: 42, team: 50 },
          { name: 'workplace.contribution', max: 160, personal: 30, team: 140 },
          { name: 'workplace.hot', max: 300, personal: 20, team: 28 },
          { name: 'workplace.yield', max: 130, personal: 35, team: 35 },
          { name: 'workplace.follow', max: 100, personal: 80, team: 90 }
        ]
      })
    })

    // 酒店信息相关API
    app.get('/hotel/info', async (req, res) => {
      try {
        const data = await dbOperations.getHotelInfo()
        res.json({
          code: 0,
          message: 'success',
          data
        })
      } catch (error) {
        console.error('获取酒店信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '获取酒店信息失败'
        })
      }
    })

    app.get('/hotel/basic', async (req, res) => {
      try {
        const data = await dbOperations.getHotelBasicInfo()
        res.json({
          code: 0,
          message: 'success',
          data
        })
      } catch (error) {
        console.error('获取酒店基本信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '获取酒店基本信息失败'
        })
      }
    })

    app.get('/hotel/budget', async (req, res) => {
      try {
        const budgetInfo = await HotelBudgetInfo.findAll({
          attributes: ['label', 'value'],
          order: [['id', 'ASC']]
        })

        res.json({
          code: 0,
          message: 'success',
          data: budgetInfo.map(item => item.toJSON())
        })
      } catch (error) {
        console.error('获取酒店预算信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '获取酒店预算信息失败'
        })
      }
    })

    app.get('/hotel/usp', async (req, res) => {
      try {
        const uspData = await HotelUspInfo.findAll({
          attributes: ['category', 'items'],
          order: [['id', 'ASC']]
        })

        // 转换USP数据格式
        const uspFormatted = uspData.reduce((acc: any, item: any) => {
          acc[item.category] = item.items
          return acc
        }, {})

        res.json({
          code: 0,
          message: 'success',
          data: [uspFormatted]
        })
      } catch (error) {
        console.error('获取酒店USP信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '获取酒店USP信息失败'
        })
      }
    })

    // 更新酒店基本信息
    app.put('/hotel/basic', async (req, res) => {
      try {
        const { basicInfo } = req.body

        if (!basicInfo || !Array.isArray(basicInfo)) {
          return res.status(400).json({
            code: 400,
            message: '无效的数据格式'
          })
        }

        const data = await dbOperations.updateHotelBasicInfo(basicInfo)

        res.json({
          code: 0,
          message: '酒店基本信息更新成功',
          data
        })
      } catch (error) {
        console.error('更新酒店基本信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '更新酒店基本信息失败'
        })
      }
    })

    // 更新酒店预算信息
    app.put('/hotel/budget', async (req, res) => {
      try {
        const { budgetInfo } = req.body

        if (!budgetInfo || !Array.isArray(budgetInfo)) {
          return res.status(400).json({
            code: 400,
            message: '无效的数据格式'
          })
        }

        const config = getDatabaseConfig()
        if (config.type === 'mysql') {
          // 删除现有预算信息
          await HotelBudgetInfo.destroy({ where: {} })

          // 插入新的预算信息
          const newBudgetInfo = await HotelBudgetInfo.bulkCreate(budgetInfo)

          res.json({
            code: 0,
            message: '酒店预算信息更新成功',
            data: newBudgetInfo.map(item => item.toJSON())
          })
        } else {
          // SQLite 处理
          await sqliteDAO.updateHotelBudgetInfo(budgetInfo)
          const data = await sqliteDAO.getHotelBudgetInfo()

          res.json({
            code: 0,
            message: '酒店预算信息更新成功',
            data
          })
        }
      } catch (error) {
        console.error('更新酒店预算信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '更新酒店预算信息失败'
        })
      }
    })

    // 更新酒店USP信息
    app.put('/hotel/usp', async (req, res) => {
      try {
        const { uspData } = req.body

        if (!uspData || !Array.isArray(uspData)) {
          return res.status(400).json({
            code: 400,
            message: '无效的数据格式'
          })
        }

        const config = getDatabaseConfig()
        if (config.type === 'mysql') {
          // 删除现有USP信息
          await HotelUspInfo.destroy({ where: {} })

          // 转换数据格式并插入
          const uspRecords = []
          if (uspData.length > 0) {
            const usp = uspData[0]
            for (const [category, items] of Object.entries(usp)) {
              uspRecords.push({
                category,
                items: JSON.stringify(items)
              })
            }
          }

          const newUspInfo = await HotelUspInfo.bulkCreate(uspRecords)

          res.json({
            code: 0,
            message: '酒店USP信息更新成功',
            data: newUspInfo.map(item => item.toJSON())
          })
        } else {
          // SQLite 处理
          await sqliteDAO.updateHotelUspInfo(uspData)
          const data = await sqliteDAO.getHotelUspInfo()

          res.json({
            code: 0,
            message: '酒店USP信息更新成功',
            data
          })
        }
      } catch (error) {
        console.error('更新酒店USP信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '更新酒店USP信息失败'
        })
      }
    })

    // 更新所有酒店信息
    app.put('/hotel/info', async (req, res) => {
      try {
        const { basicInfo, budgetInfo, uspData } = req.body

        if (!basicInfo || !budgetInfo || !uspData) {
          return res.status(400).json({
            code: 400,
            message: '缺少必要的数据字段'
          })
        }

        // 更新基本信息
        if (basicInfo && Array.isArray(basicInfo)) {
          await dbOperations.updateHotelBasicInfo(basicInfo)
        }

        // 更新预算信息
        if (budgetInfo && Array.isArray(budgetInfo)) {
          const config = getDatabaseConfig()
          if (config.type === 'mysql') {
            await HotelBudgetInfo.destroy({ where: {} })
            await HotelBudgetInfo.bulkCreate(budgetInfo)
          } else {
            await sqliteDAO.updateHotelBudgetInfo(budgetInfo)
          }
        }

        // 更新USP信息
        if (uspData && Array.isArray(uspData)) {
          const config = getDatabaseConfig()
          if (config.type === 'mysql') {
            await HotelUspInfo.destroy({ where: {} })
            const uspRecords = []
            if (uspData.length > 0) {
              const usp = uspData[0]
              for (const [category, items] of Object.entries(usp)) {
                uspRecords.push({
                  category,
                  items: JSON.stringify(items)
                })
              }
            }
            await HotelUspInfo.bulkCreate(uspRecords)
          } else {
            await sqliteDAO.updateHotelUspInfo(uspData)
          }
        }

        // 返回更新后的完整数据
        const updatedData = await dbOperations.getHotelInfo()

        res.json({
          code: 0,
          message: '酒店信息更新成功',
          data: updatedData
        })
      } catch (error) {
        console.error('更新酒店信息失败:', error)
        res.status(500).json({
          code: 500,
          message: '更新酒店信息失败'
        })
      }
    })

    // 用户相关API
    app.get('/users', async (req, res) => {
      try {
        const { page = 1, limit = 10, search = '' } = req.query
        const pageNum = Number(page)
        const limitNum = Number(limit)

        const result = await dbOperations.getUsers({
          page: pageNum,
          limit: limitNum,
          search: search as string
        })

        res.json({
          code: 0,
          message: 'success',
          data: {
            users: result.users,
            total: result.total,
            page: pageNum,
            limit: limitNum
          }
        })
      } catch (error) {
        console.error('获取用户列表失败:', error)
        res.status(500).json({
          code: 500,
          message: '获取用户列表失败'
        })
      }
    })

    app.get('/users/:id', async (req, res) => {
      try {
        const { id } = req.params
        const user = await User.findByPk(id, {
          attributes: ['id', 'username', 'email', 'role', 'createdAt', 'updatedAt']
        })

        if (user) {
          res.json({
            code: 0,
            message: 'success',
            data: user.toJSON()
          })
        } else {
          res.status(404).json({
            code: 404,
            message: '用户不存在'
          })
        }
      } catch (error) {
        console.error('获取用户失败:', error)
        res.status(500).json({
          code: 500,
          message: '获取用户失败'
        })
      }
    })

    // 创建新用户
    app.post('/users', async (req, res) => {
      try {
        const { username, email, role = 'user' } = req.body

        if (!username || !email) {
          return res.status(400).json({
            code: 400,
            message: '用户名和邮箱是必填项'
          })
        }

        // 检查用户名和邮箱是否已存在
        const existingUser = await User.findOne({
          where: {
            [Op.or]: [
              { username },
              { email }
            ]
          }
        })

        if (existingUser) {
          return res.status(400).json({
            code: 400,
            message: '用户名或邮箱已存在'
          })
        }

        const newUser = await User.create({
          username,
          email,
          role
        })

        res.status(201).json({
          code: 0,
          message: '用户创建成功',
          data: newUser.toJSON()
        })
      } catch (error) {
        console.error('创建用户失败:', error)
        res.status(500).json({
          code: 500,
          message: '创建用户失败'
        })
      }
    })

    // 更新用户
    app.put('/users/:id', async (req, res) => {
      try {
        const { id } = req.params
        const { username, email, role } = req.body

        const user = await User.findByPk(id)

        if (!user) {
          return res.status(404).json({
            code: 404,
            message: '用户不存在'
          })
        }

        // 检查用户名和邮箱是否已被其他用户使用
        if (username || email) {
          const whereCondition: any = {
            id: { [Op.ne]: id }
          }

          if (username && email) {
            whereCondition[Op.or] = [
              { username },
              { email }
            ]
          } else if (username) {
            whereCondition.username = username
          } else if (email) {
            whereCondition.email = email
          }

          const existingUser = await User.findOne({ where: whereCondition })

          if (existingUser) {
            return res.status(400).json({
              code: 400,
              message: '用户名或邮箱已被其他用户使用'
            })
          }
        }

        // 更新用户信息
        const updateData: any = {}
        if (username) updateData.username = username
        if (email) updateData.email = email
        if (role) updateData.role = role

        await user.update(updateData)

        res.json({
          code: 0,
          message: '用户更新成功',
          data: user.toJSON()
        })
      } catch (error) {
        console.error('更新用户失败:', error)
        res.status(500).json({
          code: 500,
          message: '更新用户失败'
        })
      }
    })

    // 删除用户
    app.delete('/users/:id', async (req, res) => {
      try {
        const { id } = req.params
        const user = await User.findByPk(id)

        if (!user) {
          return res.status(404).json({
            code: 404,
            message: '用户不存在'
          })
        }

        const userData = user.toJSON()
        await user.destroy()

        res.json({
          code: 0,
          message: '用户删除成功',
          data: userData
        })
      } catch (error) {
        console.error('删除用户失败:', error)
        res.status(500).json({
          code: 500,
          message: '删除用户失败'
        })
      }
    })

    // 通用错误处理
    app.use((err: any, req: any, res: any, next: any) => {
      console.error('API Error:', err.stack)
      res.status(500).json({
        code: 500,
        message: '服务器内部错误'
      })
    })

    // 404处理
    app.use('*', (req, res) => {
      res.status(404).json({
        code: 404,
        message: '接口不存在'
      })
    })

    // 启动服务器
    return new Promise((resolve, reject) => {
      server = app.listen(serverPort, '127.0.0.1', () => {
        console.log(`🚀 Electron API Server started on http://localhost:${serverPort}`)
        resolve(serverPort)
      })

      server.on('error', (err: any) => {
        console.error('Server start error:', err)
        reject(err)
      })
    })

  } catch (error) {
    console.error('Failed to start API server:', error)
    throw error
  }
}

export const stopApiServer = (): Promise<void> => {
  return new Promise(async (resolve) => {
    try {
      // 关闭数据库连接
      await closeDatabase()

      if (server) {
        server.close(() => {
          console.log('🛑 API Server stopped')
          server = null
          resolve()
        })
      } else {
        resolve()
      }
    } catch (error) {
      console.error('停止API服务器时出错:', error)
      resolve()
    }
  })
}

export const getServerPort = (): number => {
  return serverPort
}
