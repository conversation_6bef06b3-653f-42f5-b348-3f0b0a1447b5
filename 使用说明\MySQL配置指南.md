# 🗄️ MySQL 数据库集成指南

## ✅ 已完成配置

您的Electron应用已成功集成MySQL数据库！

### 📋 数据库配置信息

- **主机**: 127.0.0.1
- **端口**: 3306
- **用户名**: root
- **密码**: zy123good
- **数据库**: hiltonmarket

### 🏗️ 数据库结构

系统会自动创建以下表：

#### 1. users (用户表)
```sql
CREATE TABLE users (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  role ENUM('admin', 'user') NOT NULL DEFAULT 'user',
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);
```

#### 2. hotel_basic_info (酒店基本信息表)
```sql
CREATE TABLE hotel_basic_info (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  label VARCHAR(100) NOT NULL,
  value TEXT NOT NULL,
  note VARCHAR(200),
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);
```

#### 3. hotel_budget_info (酒店预算信息表)
```sql
CREATE TABLE hotel_budget_info (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  label VARCHAR(100) NOT NULL,
  value VARCHAR(100) NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);
```

#### 4. hotel_usp_info (酒店USP信息表)
```sql
CREATE TABLE hotel_usp_info (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  category VARCHAR(50) NOT NULL,
  items JSON NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL
);
```

## 🚀 启动和测试

### 1. 确保MySQL服务运行

```bash
# Windows (以管理员身份运行)
net start mysql

# 或者通过MySQL Workbench/phpMyAdmin检查连接
```

### 2. 启动Electron应用

```bash
npm run electron
```

### 3. 查看启动日志

应该看到以下成功信息：
```
✅ MySQL数据库连接成功
✅ 数据库表同步完成
📝 默认用户数据已创建
📝 默认酒店基本信息已创建
📝 默认预算信息已创建
📝 默认USP信息已创建
✅ 初始数据创建完成
🚀 Electron API Server started on http://localhost:8000
```

## 🔧 功能特性

### ✅ 自动化功能

1. **自动连接**: 应用启动时自动连接MySQL
2. **自动建表**: 首次运行时自动创建所有必要的表
3. **自动初始化**: 自动插入默认数据
4. **自动同步**: 模型变更时自动同步表结构
5. **自动关闭**: 应用关闭时自动断开数据库连接

### ✅ 数据持久化

- **用户管理**: 所有用户数据存储在MySQL中
- **酒店信息**: 基本信息、预算、USP数据持久化
- **数据完整性**: 支持唯一约束、外键关系
- **事务支持**: 确保数据一致性

### ✅ 高级功能

- **分页查询**: 用户列表支持分页
- **模糊搜索**: 支持用户名和邮箱搜索
- **数据验证**: 邮箱格式验证、唯一性检查
- **错误处理**: 完善的数据库错误处理

## 🧪 测试数据库连接

### 1. 手动测试连接

```bash
# 使用MySQL命令行
mysql -h 127.0.0.1 -P 3306 -u root -p
# 输入密码: zy123good

# 选择数据库
USE hiltonmarket;

# 查看表
SHOW TABLES;

# 查看用户数据
SELECT * FROM users;
```

### 2. 通过API测试

```bash
# 测试用户列表
curl http://localhost:8000/users

# 测试酒店信息
curl http://localhost:8000/hotel/info

# 测试健康检查
curl http://localhost:8000/health
```

## 🔍 故障排除

### 常见问题

#### 1. 连接失败
```
❌ 数据库连接失败: Error: connect ECONNREFUSED 127.0.0.1:3306
```

**解决方案**:
- 确保MySQL服务正在运行
- 检查端口3306是否被占用
- 验证用户名密码是否正确

#### 2. 数据库不存在
```
❌ 数据库连接失败: Error: Unknown database 'hiltonmarket'
```

**解决方案**:
```sql
-- 手动创建数据库
CREATE DATABASE hiltonmarket CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 3. 权限问题
```
❌ 数据库连接失败: Error: Access denied for user 'root'@'localhost'
```

**解决方案**:
```sql
-- 重置root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'zy123good';
FLUSH PRIVILEGES;
```

### 调试技巧

1. **启用SQL日志**: 在database.ts中已启用`logging: console.log`
2. **查看详细错误**: 检查Electron控制台输出
3. **数据库工具**: 使用MySQL Workbench或phpMyAdmin

## 📊 数据库管理

### 备份数据

```bash
# 备份整个数据库
mysqldump -h 127.0.0.1 -P 3306 -u root -p hiltonmarket > backup.sql

# 恢复数据库
mysql -h 127.0.0.1 -P 3306 -u root -p hiltonmarket < backup.sql
```

### 性能优化

1. **索引优化**: 为常用查询字段添加索引
2. **连接池**: 已配置连接池参数
3. **查询优化**: 使用Sequelize的查询优化功能

## 🎯 下一步扩展

### 可选功能

1. **数据迁移**: 使用Sequelize migrations
2. **数据种子**: 创建更多测试数据
3. **关系模型**: 添加表之间的关联关系
4. **缓存层**: 集成Redis缓存
5. **读写分离**: 配置主从数据库

### 示例扩展

```typescript
// 添加新模型
export class Hotel extends Model {
  public id!: number
  public name!: string
  public code!: string
}

// 添加关联关系
User.hasMany(Hotel, { foreignKey: 'managerId' })
Hotel.belongsTo(User, { foreignKey: 'managerId' })
```

## 🎉 总结

现在您的Electron应用具备了：

1. ✅ **完整的MySQL集成** - 自动连接和管理
2. ✅ **数据持久化** - 所有数据存储在数据库中
3. ✅ **自动化管理** - 建表、初始化、同步
4. ✅ **生产就绪** - 错误处理、连接池、事务支持
5. ✅ **易于扩展** - 基于Sequelize ORM

您的Vue3 + Node.js + Electron + MySQL全栈应用已经完成！🎊
