{"name": "vue-element-plus-admin", "version": "1.9.4", "description": "一套基于vue3、element-plus、typesScript、vite4的后台集成方案。", "main": "dist-electron/main/index.js", "author": "Archer <<EMAIL>>", "private": false, "scripts": {"i": "pnpm install", "dev": "vite --mode base", "ts:check": "vue-tsc --noEmit", "build:pro": "vite build --mode pro", "build": "vite build", "build:gitee": "vite build --mode gitee", "build:dev": "npm run ts:check && vite build --mode dev", "build:test": "npm run ts:check && vite build --mode test", "serve:pro": "vite preview --mode pro", "serve:dev": "vite preview --mode dev", "serve:test": "vite preview --mode test", "npm:check": "npx npm-check-updates", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "prepare": "husky install", "p": "plop", "analysis": "windicss-analysis", "electron": "vite", "build:electron": "vue-tsc --noEmit && vite build && electron-builder", "fix": "electron-fix start", "package": "npm run build && electron-builder --linux appImage", "server": "node server/index.js", "server:dev": "nodemon server/index.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/iconify": "^3.1.1", "@iconify/vue": "^4.1.1", "@types/sequelize": "^4.28.20", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.6.8", "cors": "^2.8.5", "cropperjs": "^1.6.1", "dayjs": "^1.11.10", "driver.js": "^1.3.1", "echarts": "^5.4.1", "echarts-wordcloud": "^2.1.0", "element-plus": "2.6.1", "express": "^4.18.2", "intro.js": "^6.0.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "mockjs": "^1.1.0", "mysql2": "^3.14.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qrcode": "^1.5.1", "qs": "^6.12.0", "sequelize": "^6.37.7", "sql.js": "^1.13.0", "url": "^0.11.3", "vite-plugin-electron": "^0.11.1", "vite-plugin-electron-renderer": "^0.12.1", "vue": "3.4.22", "vue-draggable-plus": "^0.3.5", "vue-i18n": "9.10.2", "vue-json-pretty": "^2.4.0", "vue-router": "^4.3.0", "vue-types": "^5.1.1", "xgplayer": "^3.0.14"}, "devDependencies": {"@iconify/json": "^2.2.194", "@intlify/unplugin-vue-i18n": "^3.0.1", "@purge-icons/generated": "^0.9.0", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/intro.js": "^5.1.1", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^20.11.30", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.14", "@types/sortablejs": "^1.15.8", "@unocss/transformer-variant-group": "^0.58.6", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.19", "chalk": "^5.3.0", "consola": "^3.2.3", "electron": "^30.0.0", "electron-builder": "^23.3.3", "electron-fix": "^1.1.4", "esno": "^4.7.0", "fs-extra": "^11.2.0", "inquirer": "^9.2.16", "less": "^4.2.0", "mockjs": "^1.1.0", "nodemon": "^3.1.10", "plop": "^4.0.1", "postcss": "^8.4.38", "postcss-html": "^1.6.0", "postcss-less": "^6.0.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.13.0", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "terser": "^5.29.2", "typescript": "5.4.3", "unocss": "^0.58.6", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "unplugin-vue-define-options": "^1.2.3", "vite": "5.2.2", "vite-plugin-ejs": "^1.7.0", "vite-plugin-electron": "^0.11.1", "vite-plugin-electron-renderer": "^0.12.1", "vite-plugin-mock": "2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-url-copy": "^1.1.4", "vite-plugin-windicss": "^1.8.10", "vue-tsc": "^2.0.7", "windicss": "^3.5.6", "windicss-analysis": "^0.3.5"}, "engines": {"node": ">= 14.18.0"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kailong321200875/vue-element-plus-admin.git"}, "bugs": {"url": "https://github.com/kailong321200875/vue-element-plus-admin/issues"}, "homepage": "https://github.com/kailong321200875/vue-element-plus-admin"}