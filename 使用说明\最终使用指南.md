# 🎉 Vue3 + Node.js + Electron 全栈应用使用指南

## ✅ 项目完成状态

您的Electron项目已经成功集成了Node.js后端API功能！现在编译成exe后会自动启动前后端服务。

## 🚀 快速开始

### 开发模式

```bash
# 启动Electron应用（包含集成的API服务器）
npm run electron
```

这个命令会：
1. 启动Vite开发服务器 (前端)
2. 启动Electron应用
3. 在Electron主进程中自动启动API服务器
4. 自动打开应用窗口

### 生产构建

```bash
# 构建为exe文件
npm run build:electron
```

构建后的exe文件将包含：
- Vue3前端应用
- Node.js Express API服务器
- 所有必要的依赖

## 🔧 技术实现

### 1. 集成架构

**Electron主进程** (`electron/main/index.ts`):
- 启动时自动启动Express API服务器
- 动态分配可用端口（从8000开始）
- 应用关闭时自动停止API服务器

**API服务器** (`electron/main/server.ts`):
- 完整的Express.js服务器
- 支持所有原有API功能
- 自动端口检测避免冲突

**前端渲染进程**:
- 通过IPC通信获取API服务器信息
- 动态配置axios baseURL
- 自动适配Electron环境

### 2. 自动化流程

```
启动Electron应用
    ↓
主进程启动API服务器 (端口8000+)
    ↓
渲染进程通过IPC获取API端口
    ↓
动态配置axios baseURL
    ↓
前端可以正常调用API
```

## 📋 功能验证

### 1. 在Electron应用中测试

1. **启动应用**:
   ```bash
   npm run electron
   ```

2. **查看控制台输出**:
   ```
   🚀 Electron API Server started on http://localhost:8000
   ✅ API Server started on port 8000
   ```

3. **访问功能页面**:
   - 导航到 "我的系统" → "酒店信息"
   - 点击"从API加载"按钮
   - 查看数据是否正常加载

4. **测试API演示页面**:
   - 导航到 "我的系统" → "API演示"
   - 测试健康检查、酒店信息、用户管理功能

### 2. 验证API服务器

在Electron应用运行时，API服务器会自动启动。您可以：

1. **查看健康状态**:
   - 在API演示页面点击"检查服务器状态"
   - 应该显示"服务器状态正常"

2. **测试数据操作**:
   - 在酒店信息页面测试数据加载和保存
   - 在API演示页面测试用户CRUD操作

## 🎯 部署说明

### 开发环境 vs 生产环境

**开发环境** (`npm run electron`):
- 前端: Vite开发服务器 (http://localhost:4000)
- 后端: Electron主进程中的Express服务器 (http://localhost:8000+)
- 支持热重载和调试

**生产环境** (exe文件):
- 前端: 打包到Electron渲染进程
- 后端: 集成在Electron主进程中
- 单个exe文件，无需额外依赖

### 构建和分发

1. **构建应用**:
   ```bash
   npm run build:electron
   ```

2. **输出文件**:
   - `dist/win-unpacked/` - 绿色版应用
   - `dist/*.exe` - 安装包版本

3. **分发**:
   - 直接分发exe文件
   - 用户双击即可运行
   - 无需安装Node.js或其他依赖

## 🔍 技术细节

### 1. 端口管理

```typescript
// 自动查找可用端口
const findAvailablePort = (startPort: number): Promise<number> => {
  // 从8000开始递增查找可用端口
}
```

### 2. IPC通信

```typescript
// 主进程暴露API
ipcMain.handle('get-api-port', () => getServerPort())
ipcMain.handle('get-api-status', () => ({ port, url, status }))

// 渲染进程调用
const port = await window.electronAPI.getApiPort()
```

### 3. 动态axios配置

```typescript
// 自动获取API端口并配置axios
const baseURL = await getApiBaseUrl()
axiosInstance.defaults.baseURL = baseURL
```

## 🛠️ 扩展开发

### 1. 添加新API接口

1. **后端**: 在 `electron/main/server.ts` 中添加路由
2. **前端**: 在 `src/api/hotel/index.ts` 中添加服务函数
3. **页面**: 在组件中调用新的API

### 2. 数据持久化

当前使用内存存储，可以扩展为：

```typescript
// 文件存储
import fs from 'fs'
const dataFile = path.join(app.getPath('userData'), 'data.json')

// 数据库存储
import sqlite3 from 'sqlite3'
const dbPath = path.join(app.getPath('userData'), 'database.db')
```

### 3. 自动更新

```bash
npm install electron-updater
```

## 🎊 总结

### ✅ 已完成功能

1. **完全集成**: Node.js API服务器集成到Electron主进程
2. **自动启动**: exe运行时自动启动前后端服务
3. **端口管理**: 智能端口检测和分配
4. **IPC通信**: 安全的主进程与渲染进程通信
5. **动态配置**: 自动配置API请求地址
6. **开发友好**: 支持热重载和调试
7. **生产就绪**: 单文件部署，无外部依赖

### 🎯 使用场景

- ✅ **桌面应用**: 完整的桌面端酒店管理系统
- ✅ **离线使用**: 无需网络连接即可运行
- ✅ **数据安全**: 数据存储在本地，不依赖外部服务
- ✅ **易于部署**: 单个exe文件，用户友好
- ✅ **功能完整**: 包含前端界面和后端API

### 🚀 下一步

您现在可以：

1. **继续开发**: 添加更多业务功能
2. **测试部署**: 构建exe并在其他机器上测试
3. **用户培训**: 准备用户使用文档
4. **版本管理**: 设置自动更新机制

恭喜！您的Vue3 + Node.js + Electron全栈桌面应用已经完成！🎉
