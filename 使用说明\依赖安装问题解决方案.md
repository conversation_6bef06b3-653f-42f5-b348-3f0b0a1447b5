# Node.js 项目依赖安装问题解决方案

## 🚨 常见问题
- `Socket connection timeout` 网络超时
- `ERR_SOCKET_CONNECTION_TIMEOUT` 连接超时
- `request to https://registry.npmjs.org/ failed` 请求失败
- Electron 下载失败

## 🛠️ 解决方案

### 方案一：使用国内镜像源（推荐）

#### 1. 设置 npm 镜像
```bash
# 设置淘宝镜像（npmmirror）
npm config set registry https://registry.npmmirror.com

# 查看当前源
npm config get registry

# 恢复官方源（如需要）
npm config set registry https://registry.npmjs.org
```

#### 2. 设置 pnpm 镜像
```bash
# 设置 pnpm 镜像
pnpm config set registry https://registry.npmmirror.com

# 查看配置
pnpm config get registry
```

#### 3. 设置 yarn 镜像
```bash
# 设置 yarn 镜像
yarn config set registry https://registry.npmmirror.com

# 查看配置
yarn config get registry
```

### 方案二：创建 .npmrc 配置文件（最佳实践）

在项目根目录创建 `.npmrc` 文件：

```
# 基础镜像配置
registry=https://registry.npmmirror.com

# Electron 相关镜像
electron_mirror=https://npmmirror.com/mirrors/electron/
electron_builder_binaries_mirror=https://npmmirror.com/mirrors/electron-builder-binaries/
ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/

# 其他常用镜像
sass_binary_site=https://npmmirror.com/mirrors/node-sass/
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs/
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver/
```

### 方案三：使用 nrm 管理镜像源

#### 1. 安装 nrm
```bash
npm install -g nrm
```

#### 2. 使用 nrm
```bash
# 查看可用源
nrm ls

# 切换到淘宝源
nrm use taobao

# 测试源速度
nrm test

# 添加自定义源
nrm add <name> <url>
```

## 📋 完整安装流程

### 1. 检查项目配置
```bash
# 查看 package.json 中推荐的包管理器
cat package.json | grep -A 5 -B 5 "scripts"
```

### 2. 选择合适的包管理器
- 如果项目有 `pnpm-lock.yaml` → 使用 `pnpm`
- 如果项目有 `yarn.lock` → 使用 `yarn`  
- 如果项目有 `package-lock.json` → 使用 `npm`

### 3. 配置镜像并安装
```bash
# 方法1：直接设置镜像
npm config set registry https://registry.npmmirror.com
npm install

# 方法2：创建 .npmrc 文件后安装
pnpm install

# 方法3：临时使用镜像
npm install --registry https://registry.npmmirror.com
```

## 🔧 特殊情况处理

### Electron 项目
```bash
# 设置 Electron 下载镜像
export ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/
npm install

# 或在 Windows 中
set ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/
npm install
```

### Python 项目（如果有）
```bash
# 设置 pip 镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 清理缓存
```bash
# npm 清理缓存
npm cache clean --force

# pnpm 清理缓存  
pnpm store prune

# yarn 清理缓存
yarn cache clean
```

## 🌐 常用镜像源

| 名称 | 地址 | 说明 |
|------|------|------|
| 官方源 | https://registry.npmjs.org | 国外访问慢 |
| 淘宝镜像 | https://registry.npmmirror.com | 推荐使用 |
| 腾讯镜像 | https://mirrors.cloud.tencent.com/npm/ | 备选方案 |
| 华为镜像 | https://mirrors.huaweicloud.com/repository/npm/ | 备选方案 |

## 💡 最佳实践

1. **项目级配置**：在项目根目录创建 `.npmrc` 文件
2. **团队协作**：将 `.npmrc` 提交到版本控制
3. **CI/CD**：在构建脚本中设置镜像源
4. **定期更新**：定期检查镜像源的可用性

## 🚀 验证安装

```bash
# 检查安装是否成功
npm list --depth=0

# 启动开发服务器
npm run dev
# 或
pnpm dev
# 或  
yarn dev
```

---
**创建时间**: 2024年
**适用场景**: Node.js、Vue、React、Angular 等前端项目
**维护建议**: 定期更新镜像地址，关注官方公告
