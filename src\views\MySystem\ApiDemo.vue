<template>
  <ContentWrap>
    <div class="api-demo-container">
      <h1>Node.js API 演示</h1>

      <!-- 健康检查 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span>健康检查</span>
            <el-button type="primary" @click="checkHealth">检查服务器状态</el-button>
          </div>
        </template>
        <div v-if="healthStatus">
          <p><strong>状态:</strong> {{ healthStatus.status }}</p>
          <p><strong>消息:</strong> {{ healthStatus.message }}</p>
          <p><strong>端口:</strong> {{ healthStatus.port }}</p>
          <p><strong>时间:</strong> {{ healthStatus.timestamp }}</p>
          <div
            v-if="healthStatus.database"
            style="margin-top: 15px; padding: 10px; background-color: #f5f5f5; border-radius: 4px"
          >
            <h4 style="margin: 0 0 10px 0">数据库信息</h4>
            <p
              ><strong>类型:</strong>
              <span
                :style="{ color: healthStatus.database.type === 'MySQL' ? '#e6a23c' : '#409eff' }"
                >{{ healthStatus.database.type }}</span
              ></p
            >
            <p><strong>连接:</strong> {{ healthStatus.database.connection }}</p>
            <p><strong>描述:</strong> {{ healthStatus.database.description }}</p>
          </div>
        </div>
      </el-card>

      <!-- 酒店信息 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span>酒店信息管理</span>
            <div>
              <el-button type="primary" @click="loadHotelInfo">加载酒店信息</el-button>
              <el-button type="success" @click="updateHotelInfo">更新基本信息</el-button>
            </div>
          </div>
        </template>

        <div v-if="hotelInfo">
          <h3>基本信息</h3>
          <el-table :data="hotelInfo.basicInfo" style="width: 100%; margin-bottom: 20px">
            <el-table-column prop="label" label="项目" width="200" />
            <el-table-column prop="value" label="内容" />
            <el-table-column prop="note" label="备注" />
          </el-table>

          <h3>预算信息</h3>
          <el-table :data="hotelInfo.budgetInfo" style="width: 100%; margin-bottom: 20px">
            <el-table-column prop="label" label="预算项目" />
            <el-table-column prop="value" label="金额" />
          </el-table>
        </div>
      </el-card>

      <!-- 数据库管理 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span>数据库管理</span>
            <div>
              <el-button @click="loadDatabaseInfo">获取信息</el-button>
              <el-button
                type="primary"
                @click="quickSwitchDatabase"
                :loading="switchingDb"
                :disabled="!databaseInfo"
                style="margin-right: 10px"
              >
                {{
                  databaseInfo
                    ? `一键切换到${databaseInfo.type === 'MySQL' ? 'SQLite' : 'MySQL'}`
                    : '一键切换'
                }}
              </el-button>
              <el-button type="warning" @click="switchToMySQL" :loading="switchingDb"
                >切换到MySQL</el-button
              >
              <el-button type="info" @click="switchToSQLite" :loading="switchingDb"
                >切换到SQLite</el-button
              >
            </div>
          </div>
        </template>

        <div v-if="databaseInfo">
          <div style="margin-bottom: 15px">
            <p
              ><strong>当前数据库:</strong>
              <span
                :style="{
                  color: databaseInfo.type === 'MySQL' ? '#e6a23c' : '#409eff',
                  fontSize: '16px',
                  fontWeight: 'bold'
                }"
                >{{ databaseInfo.type }}</span
              >
            </p>
            <p
              ><strong>连接地址:</strong> <code>{{ databaseInfo.connection }}</code></p
            >
            <p><strong>描述:</strong> {{ databaseInfo.description }}</p>
          </div>

          <div
            style="
              padding: 15px;
              background-color: #f0f9ff;
              border-left: 4px solid #409eff;
              border-radius: 4px;
            "
          >
            <h4 style="margin: 0 0 10px 0; color: #409eff">💡 数据库选择建议</h4>
            <ul style="margin: 0; padding-left: 20px">
              <li><strong>SQLite</strong>: 适合开发环境、小型应用、单用户场景（使用sql.js实现）</li>
              <li><strong>MySQL</strong>: 适合生产环境、多用户、高并发场景</li>
            </ul>
          </div>
        </div>
      </el-card>

      <!-- 用户管理 -->
      <el-card class="demo-card">
        <template #header>
          <div class="card-header">
            <span>用户管理</span>
            <div>
              <el-input
                v-model="searchText"
                placeholder="搜索用户"
                style="width: 200px; margin-right: 10px"
                @input="searchUsers"
              />
              <el-button type="primary" @click="loadUsers">刷新列表</el-button>
              <el-button type="success" @click="showCreateDialog">新增用户</el-button>
            </div>
          </div>
        </template>

        <el-table :data="users" style="width: 100%" v-loading="loading">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column prop="role" label="角色" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" @click="editUser(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="removeUser(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="totalUsers"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 用户编辑对话框 -->
      <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑用户' : '新增用户'" width="500px">
        <el-form :model="userForm" label-width="80px">
          <el-form-item label="用户名">
            <el-input v-model="userForm.username" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="userForm.email" />
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="userForm.role" style="width: 100%">
              <el-option label="管理员" value="admin" />
              <el-option label="用户" value="user" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUser">保存</el-button>
        </template>
      </el-dialog>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import {
  healthCheck,
  getHotelInfo,
  updateHotelBasicInfo,
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  getDatabaseInfo,
  switchDatabase,
  type User,
  type UserListParams,
  type DatabaseInfo
} from '@/api/hotel'

// 健康检查相关
const healthStatus = ref<any>(null)

// 酒店信息相关
const hotelInfo = ref<any>(null)

// 数据库管理相关
const databaseInfo = ref<DatabaseInfo | null>(null)
const switchingDb = ref(false)

// 用户管理相关
const users = ref<User[]>([])
const totalUsers = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchText = ref('')
const loading = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const userForm = ref<Partial<User>>({
  username: '',
  email: '',
  role: 'user'
})

// 健康检查
const checkHealth = async () => {
  try {
    const res = await healthCheck()
    healthStatus.value = res.data
    ElMessage.success('服务器状态正常')
  } catch (error) {
    ElMessage.error('服务器连接失败')
    console.error('Health check failed:', error)
  }
}

// 加载酒店信息
const loadHotelInfo = async () => {
  try {
    const res = await getHotelInfo()
    hotelInfo.value = res.data
    ElMessage.success('酒店信息加载成功')
  } catch (error) {
    ElMessage.error('加载酒店信息失败')
    console.error('Load hotel info failed:', error)
  }
}

// 更新酒店信息
const updateHotelInfo = async () => {
  if (!hotelInfo.value) {
    ElMessage.warning('请先加载酒店信息')
    return
  }

  try {
    // 模拟更新第一条基本信息
    const updatedBasicInfo = [...hotelInfo.value.basicInfo]
    updatedBasicInfo[0].value = 'Bob (Updated)'

    const res = await updateHotelBasicInfo({ basicInfo: updatedBasicInfo })
    hotelInfo.value.basicInfo = res.data
    ElMessage.success('酒店基本信息更新成功')
  } catch (error) {
    ElMessage.error('更新酒店信息失败')
    console.error('Update hotel info failed:', error)
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    loading.value = true
    const params: UserListParams = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchText.value
    }
    const res = await getUserList(params)
    users.value = res.data.users || []
    totalUsers.value = res.data.total || 0
  } catch (error) {
    ElMessage.error('加载用户列表失败')
    console.error('Load users failed:', error)
    users.value = []
    totalUsers.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索用户
const searchUsers = () => {
  currentPage.value = 1
  loadUsers()
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  loadUsers()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  loadUsers()
}

// 显示创建对话框
const showCreateDialog = () => {
  isEdit.value = false
  userForm.value = {
    username: '',
    email: '',
    role: 'user'
  }
  dialogVisible.value = true
}

// 编辑用户
const editUser = (user: User) => {
  isEdit.value = true
  userForm.value = { ...user }
  dialogVisible.value = true
}

// 保存用户
const saveUser = async () => {
  try {
    if (isEdit.value) {
      await updateUser(userForm.value.id!, userForm.value)
      ElMessage.success('用户更新成功')
    } else {
      await createUser(userForm.value as Omit<User, 'id'>)
      ElMessage.success('用户创建成功')
    }
    dialogVisible.value = false
    loadUsers()
  } catch (error) {
    ElMessage.error(isEdit.value ? '用户更新失败' : '用户创建失败')
    console.error('Save user failed:', error)
  }
}

// 删除用户
const removeUser = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个用户吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteUser(id)
    ElMessage.success('用户删除成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('用户删除失败')
      console.error('Delete user failed:', error)
    }
  }
}

// 获取数据库信息
const loadDatabaseInfo = async () => {
  try {
    const res = await getDatabaseInfo()
    databaseInfo.value = res.data
    ElMessage.success('数据库信息获取成功')
  } catch (error) {
    ElMessage.error('获取数据库信息失败')
    console.error('Get database info failed:', error)
  }
}

// 切换到MySQL
const switchToMySQL = async () => {
  try {
    await ElMessageBox.confirm('切换到MySQL数据库将重新连接数据库，确定继续吗？', '确认切换', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    switchingDb.value = true
    const res = await switchDatabase('mysql')
    databaseInfo.value = res.data
    ElMessage.success('成功切换到MySQL数据库')

    // 重新加载数据
    await loadUsers()
    await loadHotelInfo()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('切换到MySQL失败')
      console.error('Switch to MySQL failed:', error)
    }
  } finally {
    switchingDb.value = false
  }
}

// 切换到SQLite
const switchToSQLite = async () => {
  try {
    await ElMessageBox.confirm('切换到SQLite数据库将重新连接数据库，确定继续吗？', '确认切换', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    switchingDb.value = true
    const res = await switchDatabase('sqlite')
    databaseInfo.value = res.data
    ElMessage.success('成功切换到SQLite数据库')

    // 重新加载数据
    await loadUsers()
    await loadHotelInfo()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('切换到SQLite失败')
      console.error('Switch to SQLite failed:', error)
    }
  } finally {
    switchingDb.value = false
  }
}

// 一键切换数据库
const quickSwitchDatabase = async () => {
  if (!databaseInfo.value) {
    ElMessage.warning('请先获取数据库信息')
    return
  }

  const currentType = databaseInfo.value.type
  const targetType = currentType === 'MySQL' ? 'SQLite' : 'MySQL'

  try {
    await ElMessageBox.confirm(
      `当前使用${currentType}数据库，确定要一键切换到${targetType}数据库吗？`,
      '一键切换确认',
      {
        confirmButtonText: '确定切换',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    switchingDb.value = true
    const res = await switchDatabase(targetType.toLowerCase() as 'mysql' | 'sqlite')
    databaseInfo.value = res.data
    ElMessage.success(`🎉 一键切换成功！已切换到${targetType}数据库`)

    // 重新加载数据
    await loadUsers()
    await loadHotelInfo()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`一键切换到${targetType}失败`)
      console.error('Quick switch database failed:', error)
    }
  } finally {
    switchingDb.value = false
  }
}

// 页面加载时执行
onMounted(() => {
  checkHealth()
  loadUsers()
  loadDatabaseInfo()
})
</script>

<style scoped>
.api-demo-container {
  padding: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}
</style>
