# Node.js API 后端服务

这是一个简单的 Express.js 后端服务，为 Vue3 前端项目提供 API 接口。

## 功能特性

- ✅ RESTful API 设计
- ✅ CORS 跨域支持
- ✅ JSON 数据处理
- ✅ 错误处理中间件
- ✅ 酒店信息管理
- ✅ 用户 CRUD 操作
- ✅ 分页和搜索功能
- ✅ 健康检查接口

## 安装依赖

```bash
# 安装后端依赖
npm install express cors nodemon

# 或者使用 pnpm
pnpm install express cors nodemon
```

## 启动服务

```bash
# 开发模式（自动重启）
npm run server:dev

# 生产模式
npm run server
```

服务器将在 `http://localhost:8000` 启动

## API 接口文档

### 健康检查
- **GET** `/health` - 检查服务器状态

### 酒店信息管理
- **GET** `/hotel/info` - 获取酒店完整信息
- **GET** `/hotel/basic` - 获取酒店基本信息
- **GET** `/hotel/budget` - 获取酒店预算信息
- **GET** `/hotel/usp` - 获取酒店USP信息
- **PUT** `/hotel/basic` - 更新酒店基本信息

### 用户管理
- **GET** `/users` - 获取用户列表（支持分页和搜索）
  - 查询参数：`page`, `limit`, `search`
- **GET** `/users/:id` - 获取单个用户
- **POST** `/users` - 创建新用户
- **PUT** `/users/:id` - 更新用户信息
- **DELETE** `/users/:id` - 删除用户

## 数据格式

### 标准响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 用户对象
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "role": "admin"
}
```

### 酒店基本信息
```json
{
  "label": "计划制定人",
  "value": "Alice",
  "note": "用户手填"
}
```

## 前端集成

### 1. 关闭 Mock 服务
在 `.env.base` 文件中设置：
```env
VITE_USE_MOCK=false
```

### 2. 使用 API 服务
```typescript
import { getHotelInfo, getUserList } from '@/api/hotel'

// 获取酒店信息
const hotelData = await getHotelInfo()

// 获取用户列表
const users = await getUserList({ page: 1, limit: 10 })
```

### 3. 访问演示页面
启动前端项目后，访问 `/my-system/api-demo` 页面查看 API 演示。

## 开发说明

### 项目结构
```
server/
├── index.js          # 主服务器文件
├── README.md         # 说明文档
└── uploads/          # 文件上传目录（可选）
```

### 扩展功能

1. **数据库集成**
   ```bash
   npm install mongoose  # MongoDB
   # 或
   npm install mysql2 sequelize  # MySQL
   ```

2. **身份验证**
   ```bash
   npm install jsonwebtoken bcryptjs
   ```

3. **文件上传**
   ```bash
   npm install multer
   ```

4. **数据验证**
   ```bash
   npm install joi
   ```

5. **日志记录**
   ```bash
   npm install winston
   ```

## 部署建议

### 开发环境
- 使用 `nodemon` 自动重启
- 启用详细错误信息
- 使用内存数据存储

### 生产环境
- 使用 PM2 进程管理
- 连接真实数据库
- 启用日志记录
- 配置环境变量
- 使用 HTTPS

### PM2 部署示例
```bash
# 安装 PM2
npm install -g pm2

# 启动服务
pm2 start server/index.js --name "api-server"

# 查看状态
pm2 status

# 查看日志
pm2 logs api-server
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   netstat -ano | findstr :8000
   
   # 或修改端口
   set PORT=8001 && npm run server
   ```

2. **CORS 错误**
   - 确保已安装并配置 `cors` 中间件
   - 检查前端请求的域名是否正确

3. **API 请求失败**
   - 检查服务器是否正常启动
   - 确认 API 路径是否正确
   - 查看浏览器网络面板的错误信息

### 调试技巧

1. **启用详细日志**
   ```javascript
   app.use((req, res, next) => {
     console.log(`${req.method} ${req.path}`, req.body);
     next();
   });
   ```

2. **使用 Postman 测试**
   - 直接测试 API 接口
   - 验证请求和响应格式

3. **检查网络代理**
   - 确认 Vite 代理配置正确
   - 检查目标服务器地址
