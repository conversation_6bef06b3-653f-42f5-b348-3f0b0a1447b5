import { Sequelize, DataTypes, Model } from 'sequelize'
import {
  createSequelizeInstance,
  validateConfig,
  getDatabaseInfo,
  loadConfigFromEnv,
  setDatabaseType,
  DB_TYPES,
  initSQLiteAdapter,
  closeSQLiteAdapter,
  getDatabaseConfig
} from './database-config'
import { sqliteDAO } from './sqlite-dao'

// 创建Sequelize实例
let sequelize: Sequelize

// 初始化数据库连接
export const initSequelize = async (): Promise<Sequelize> => {
  // 从环境变量加载配置
  loadConfigFromEnv()

  // 创建数据库实例
  sequelize = createSequelizeInstance()

  // 验证连接
  const isValid = await validateConfig(sequelize)
  if (!isValid) {
    throw new Error('数据库连接验证失败')
  }

  return sequelize
}

// 获取当前数据库实例
export const getSequelize = (): Sequelize => {
  const config = getDatabaseConfig()
  if (config.type === 'sqlite') {
    throw new Error('SQLite模式下不使用Sequelize，请使用SQLiteAdapter')
  }
  if (!sequelize) {
    throw new Error('数据库未初始化，请先调用 initSequelize()')
  }
  return sequelize
}

// 切换数据库类型
export const switchDatabase = async (type: 'mysql' | 'sqlite'): Promise<void> => {
  console.log(`🔄 正在切换到 ${type.toUpperCase()} 数据库...`)

  // 关闭当前连接
  if (sequelize) {
    await sequelize.close()
  }

  // 如果当前是SQLite，关闭SQLite适配器
  if (getDatabaseConfig().type === 'sqlite') {
    closeSQLiteAdapter()
  }

  // 设置新的数据库类型
  setDatabaseType(type)

  if (type === 'sqlite') {
    // 对于SQLite，只初始化适配器，不使用Sequelize
    await initSQLiteAdapter()
    // SQLite模式下不需要Sequelize实例
    sequelize = null as any
  } else {
    // 对于MySQL，正常初始化Sequelize
    sequelize = await initSequelize()
    // 重新定义模型
    defineModels()
  }

  console.log(`✅ 已成功切换到 ${type.toUpperCase()} 数据库`)
}

// 用户模型接口
interface UserAttributes {
  id: number
  username: string
  email: string
  role: string
  createdAt?: Date
  updatedAt?: Date
}

interface UserCreationAttributes extends Omit<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// 用户模型
export class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  declare id: number
  declare username: string
  declare email: string
  declare role: string
  declare readonly createdAt: Date
  declare readonly updatedAt: Date
}

// 酒店基本信息模型接口
interface HotelBasicInfoAttributes {
  id: number
  label: string
  value: string
  note?: string
  createdAt?: Date
  updatedAt?: Date
}

interface HotelBasicInfoCreationAttributes extends Omit<HotelBasicInfoAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// 酒店基本信息模型
export class HotelBasicInfo extends Model<HotelBasicInfoAttributes, HotelBasicInfoCreationAttributes> implements HotelBasicInfoAttributes {
  declare id: number
  declare label: string
  declare value: string
  declare note?: string
  declare readonly createdAt: Date
  declare readonly updatedAt: Date
}

// 酒店预算信息模型接口
interface HotelBudgetInfoAttributes {
  id: number
  label: string
  value: string
  createdAt?: Date
  updatedAt?: Date
}

interface HotelBudgetInfoCreationAttributes extends Omit<HotelBudgetInfoAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// 酒店预算信息模型
export class HotelBudgetInfo extends Model<HotelBudgetInfoAttributes, HotelBudgetInfoCreationAttributes> implements HotelBudgetInfoAttributes {
  declare id: number
  declare label: string
  declare value: string
  declare readonly createdAt: Date
  declare readonly updatedAt: Date
}

// 酒店USP信息模型接口
interface HotelUspInfoAttributes {
  id: number
  category: string
  items: string | string[]
  createdAt?: Date
  updatedAt?: Date
}

interface HotelUspInfoCreationAttributes extends Omit<HotelUspInfoAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// 酒店USP信息模型
export class HotelUspInfo extends Model<HotelUspInfoAttributes, HotelUspInfoCreationAttributes> implements HotelUspInfoAttributes {
  declare id: number
  declare category: string
  declare items: string | string[]
  declare readonly createdAt: Date
  declare readonly updatedAt: Date
}

// 定义所有模型
export const defineModels = (): void => {
  const seq = getSequelize()

  // 用户模型
  User.init(
    {
      id: {
        type: DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true
        }
      },
      role: {
        type: DataTypes.ENUM('admin', 'user'),
        allowNull: false,
        defaultValue: 'user'
      }
    },
    {
      sequelize: seq,
      tableName: 'users',
      timestamps: true
    }
  )

  // 酒店基本信息模型
  HotelBasicInfo.init(
    {
      id: {
        type: DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      label: {
        type: DataTypes.STRING(100),
        allowNull: false
      },
      value: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      note: {
        type: DataTypes.STRING(200),
        allowNull: true
      }
    },
    {
      sequelize: seq,
      tableName: 'hotel_basic_info',
      timestamps: true
    }
  )

  // 酒店预算信息模型
  HotelBudgetInfo.init(
    {
      id: {
        type: DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      label: {
        type: DataTypes.STRING(100),
        allowNull: false
      },
      value: {
        type: DataTypes.STRING(100),
        allowNull: false
      }
    },
    {
      sequelize: seq,
      tableName: 'hotel_budget_info',
      timestamps: true
    }
  )

  // 酒店USP信息模型
  HotelUspInfo.init(
    {
      id: {
        type: DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      category: {
        type: DataTypes.STRING(50),
        allowNull: false
      },
      items: {
        type: DataTypes.JSON,
        allowNull: false
      }
    },
    {
      sequelize: seq,
      tableName: 'hotel_usp_info',
      timestamps: true
    }
  )
}

// 数据库连接和初始化（优化版本）
export const initDatabase = async (): Promise<void> => {
  const startTime = Date.now()
  console.log('🚀 开始数据库初始化...')

  try {
    // 从环境变量加载配置
    loadConfigFromEnv()

    const config = getDatabaseConfig()
    const dbInfo = getDatabaseInfo()
    console.log(`📊 当前使用: ${dbInfo.description}`)

    if (config.type === 'mysql') {
      console.log('🔗 初始化MySQL数据库...')
      const mysqlStartTime = Date.now()

      // MySQL初始化
      await initSequelize()
      defineModels()
      console.log(`✅ MySQL连接建立 (${Date.now() - mysqlStartTime}ms)`)

      // 并行检查表和数据
      const [tableExists, needsInitData] = await Promise.all([
        checkTablesExist(),
        checkNeedsInitialization().catch(() => true) // 如果检查失败，假设需要初始化
      ])

      if (!tableExists) {
        console.log('🔧 首次运行，创建数据库表...')
        const syncStartTime = Date.now()
        await getSequelize().sync({ force: false })
        console.log(`✅ 数据库表创建完成 (${Date.now() - syncStartTime}ms)`)

        // 初始化数据
        const dataStartTime = Date.now()
        await initializeData()
        console.log(`✅ 初始数据创建完成 (${Date.now() - dataStartTime}ms)`)
      } else {
        console.log('✅ 数据库表已存在，跳过同步')

        if (needsInitData) {
          const dataStartTime = Date.now()
          await initializeData()
          console.log(`✅ 初始数据补充完成 (${Date.now() - dataStartTime}ms)`)
        } else {
          console.log('✅ 数据已存在，跳过初始化')
        }
      }
    } else {
      console.log('🔗 初始化SQLite数据库...')
      // SQLite初始化
      await initSQLiteAdapter()
    }

    console.log(`🎉 数据库初始化完成！总耗时: ${Date.now() - startTime}ms`)

  } catch (error) {
    console.error(`❌ 数据库初始化失败 (耗时: ${Date.now() - startTime}ms):`, error)
    throw error
  }
}

// 检查表是否存在
const checkTablesExist = async (): Promise<boolean> => {
  try {
    const config = getDatabaseConfig()

    if (config.type === 'mysql') {
      const seq = getSequelize()
      // MySQL查询
      const [results] = await seq.query(`
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = 'hiltonmarket'
        AND table_name IN ('users', 'hotel_basic_info', 'hotel_budget_info', 'hotel_usp_info')
      `)
      return (results as any)[0].count === 4
    } else {
      // SQLite查询
      return await sqliteDAO.checkTablesExist()
    }
  } catch (error) {
    return false
  }
}

// 检查是否需要初始化数据
const checkNeedsInitialization = async (): Promise<boolean> => {
  try {
    const config = getDatabaseConfig()

    if (config.type === 'mysql') {
      const userCount = await User.count()
      return userCount === 0
    } else {
      return await sqliteDAO.checkNeedsInitialization()
    }
  } catch (error) {
    return true
  }
}

// 初始化默认数据
const initializeData = async (): Promise<void> => {
  try {
    // 使用事务批量插入，提高性能
    await sequelize.transaction(async (t) => {
      // 批量创建所有默认数据
      await Promise.all([
        // 创建默认用户
        User.bulkCreate([
          { username: 'admin', email: '<EMAIL>', role: 'admin' },
          { username: 'user', email: '<EMAIL>', role: 'user' }
        ], { transaction: t, ignoreDuplicates: true }),

        // 创建默认酒店基本信息
        HotelBasicInfo.bulkCreate([
          { label: '计划制定人', value: 'Alice', note: '用户手填' },
          { label: '酒店In Code', value: 'AOGCN', note: 'incode预处理数据' },
          { label: '酒店名称', value: '九寨沟康莱德酒店', note: '预填与incode匹配' },
          { label: '酒店所在区域', value: '西区', note: '预填与incode匹配' },
          { label: '总经理', value: 'A', note: '用户手填' },
          { label: '商务总监', value: 'B', note: '用户手填' },
          { label: '市场总监', value: 'C', note: '用户手填' },
          { label: 'MECC 联系人', value: 'D', note: '用户手填' }
        ], { transaction: t, ignoreDuplicates: true }),

        // 创建默认预算信息
        HotelBudgetInfo.bulkCreate([
          { label: '酒店本地活动预算', value: '¥620,125.00' },
          { label: '集团市场共享费（Co-op Fund）', value: '¥349,561.33' },
          { label: 'PMP', value: '¥60,000.00' },
          { label: '总预算', value: '¥1,029,686.33' }
        ], { transaction: t, ignoreDuplicates: true }),

        // 创建默认USP信息
        HotelUspInfo.bulkCreate([
          { category: 'rooms', items: ['家庭房', '景观房', '独特风格房型', '亲子房'] },
          { category: 'dining', items: ['免费早餐', '餐厅拥有佳景', '国际美食'] },
          { category: 'meeting', items: ['1000平米无柱宴会厅', '40平米高清LED', '10种会议室组合'] },
          { category: 'services', items: ['室外泳池/儿童泳池', 'SPA', '运动中心、健身房'] }
        ], { transaction: t, ignoreDuplicates: true })
      ])
    })

    console.log('📝 默认数据初始化完成')

  } catch (error) {
    console.error('❌ 初始化数据失败:', error)
    throw error
  }
}

// 关闭数据库连接
export const closeDatabase = async (): Promise<void> => {
  try {
    const config = getDatabaseConfig()

    if (config.type === 'mysql') {
      if (sequelize) {
        await sequelize.close()
        console.log('✅ MySQL数据库连接已关闭')
      }
    } else {
      closeSQLiteAdapter()
      console.log('✅ SQLite数据库已关闭')
    }
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error)
  }
}
