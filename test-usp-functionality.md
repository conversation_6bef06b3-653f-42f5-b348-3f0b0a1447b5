# USP表格化功能测试指南

## 测试目标
验证酒店USP（独特卖点）部分的表格化重构是否正常工作。

## 测试步骤

### 1. 页面访问测试
1. 启动应用程序：`npm run dev`
2. 在浏览器中访问：`http://localhost:4001/`
3. 登录系统（如果需要）
4. 导航到"我的系统" -> "酒店信息管理"页面

### 2. USP表格显示测试
验证以下元素是否正确显示：

#### 主卡片结构
- [ ] USP主卡片使用希尔顿蓝色边框
- [ ] 主卡片标题显示"酒店USP（独特卖点）"
- [ ] 副标题说明文字正确显示

#### 子卡片结构
- [ ] 客房与餐饮特色合并子卡片正确显示
- [ ] 会议及宴会特色独立子卡片正确显示
- [ ] 其他服务特色独立子卡片正确显示

#### 表格结构
每个表格应包含：
- [ ] 表格标题栏使用希尔顿蓝色背景
- [ ] "特色项目"列（宽度200px）
- [ ] "描述"列（自适应宽度）
- [ ] "操作"列（宽度80px）
- [ ] "新增行"按钮（希尔顿金色）

### 3. 功能交互测试

#### 新增行功能
1. 点击任意表格的"➕ 新增行"按钮
2. 验证：
   - [ ] 新行成功添加到表格末尾
   - [ ] 新行包含空的输入框
   - [ ] 输入框可以正常获得焦点

#### 删除行功能
1. 点击任意行的"🗑️"删除按钮
2. 验证：
   - [ ] 行成功删除
   - [ ] 如果只剩一行，显示警告信息"至少保留一行数据"

#### 输入框功能
1. 在"特色项目"和"描述"输入框中输入内容
2. 验证：
   - [ ] 输入内容正确保存
   - [ ] 输入框样式符合希尔顿设计规范
   - [ ] 焦点状态样式正确

### 4. 数据保存测试
1. 修改表格中的数据
2. 点击页面顶部的"✅ 保存所有数据"按钮
3. 验证：
   - [ ] 显示保存成功消息
   - [ ] 数据正确保存到后端
   - [ ] 页面刷新后数据仍然存在

### 5. 响应式设计测试
1. 调整浏览器窗口大小
2. 验证：
   - [ ] 在大屏幕上，客房和餐饮表格并排显示
   - [ ] 在中等屏幕上，表格垂直排列
   - [ ] 在小屏幕上，表格适当缩放

### 6. 样式验证测试
验证以下样式是否符合希尔顿品牌规范：

#### 颜色使用
- [ ] 主要蓝色：#002F61 或 #007293
- [ ] 金色强调：#D4AF37
- [ ] 白色背景：#FFFFFF

#### 字体和间距
- [ ] 标题字体大小适中
- [ ] 间距紧凑但不拥挤
- [ ] 整体布局专业现代

## 预期结果
所有测试项目都应该通过，USP部分应该：
1. 显示为现代化的表格界面
2. 支持动态添加和删除行
3. 保持希尔顿品牌设计风格
4. 在不同屏幕尺寸下正常工作
5. 数据能够正确保存和加载

## 问题报告
如果发现任何问题，请记录：
1. 问题描述
2. 重现步骤
3. 预期行为
4. 实际行为
5. 浏览器和版本信息

## 测试完成确认
- [ ] 所有功能测试通过
- [ ] 样式符合设计要求
- [ ] 响应式设计正常
- [ ] 数据保存功能正常
- [ ] 用户体验良好
