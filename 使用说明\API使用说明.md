# Vue3 + Node.js API 集成使用说明

## 🎯 项目概述

本项目成功集成了 Vue3 前端和 Node.js Express 后端，实现了完整的前后端分离架构。

## 🚀 快速开始

### 1. 启动后端服务器

```bash
# 开发模式（推荐）
npm run server:dev

# 生产模式
npm run server
```

后端服务器将在 `http://localhost:8000` 启动

### 2. 启动前端开发服务器

```bash
npm run dev
```

前端服务器将在 `http://localhost:4000` 启动

### 3. 访问演示页面

- **酒店信息页面**: `http://localhost:4000/#/my-system/hotel-info`
- **API演示页面**: `http://localhost:4000/#/my-system/api-demo`

## 📋 功能特性

### ✅ 已实现功能

1. **后端API服务**
   - Express.js 服务器
   - RESTful API 设计
   - CORS 跨域支持
   - 错误处理中间件
   - 健康检查接口

2. **酒店信息管理**
   - 获取酒店基本信息
   - 获取预算信息
   - 获取USP信息
   - 更新酒店信息

3. **用户管理系统**
   - 用户列表（分页、搜索）
   - 创建用户
   - 更新用户
   - 删除用户

4. **前端集成**
   - Axios HTTP 客户端
   - API 服务封装
   - 错误处理
   - 加载状态管理

### 🔧 技术栈

**后端**
- Node.js
- Express.js
- CORS
- Nodemon（开发）

**前端**
- Vue 3
- TypeScript
- Element Plus
- Axios
- Vite

## 📁 项目结构

```
├── server/                 # 后端服务器
│   ├── index.js           # 主服务器文件
│   └── README.md          # 后端说明文档
├── src/
│   ├── api/
│   │   └── hotel/         # API 服务封装
│   │       └── index.ts
│   └── views/
│       └── MySystem/      # 系统页面
│           ├── HotelInfo.vue    # 酒店信息页面
│           └── ApiDemo.vue      # API演示页面
└── package.json           # 项目配置
```

## 🔌 API 接口

### 健康检查
- **GET** `/health` - 检查服务器状态

### 酒店信息
- **GET** `/hotel/info` - 获取完整酒店信息
- **GET** `/hotel/basic` - 获取基本信息
- **GET** `/hotel/budget` - 获取预算信息
- **GET** `/hotel/usp` - 获取USP信息
- **PUT** `/hotel/basic` - 更新基本信息

### 用户管理
- **GET** `/users` - 获取用户列表
- **GET** `/users/:id` - 获取单个用户
- **POST** `/users` - 创建用户
- **PUT** `/users/:id` - 更新用户
- **DELETE** `/users/:id` - 删除用户

## 💡 使用示例

### 1. 在酒店信息页面

1. 点击"从API加载"按钮从后端获取数据
2. 数据加载后页面切换到"API模式"
3. 可以点击"保存到API"将修改保存到后端

### 2. 在API演示页面

1. 查看服务器健康状态
2. 管理酒店信息
3. 进行用户CRUD操作
4. 体验分页和搜索功能

### 3. 代码示例

```typescript
// 获取酒店信息
import { getHotelInfo } from '@/api/hotel'

const loadHotelData = async () => {
  try {
    const res = await getHotelInfo()
    console.log('酒店数据:', res.data)
  } catch (error) {
    console.error('加载失败:', error)
  }
}

// 创建用户
import { createUser } from '@/api/hotel'

const addUser = async () => {
  try {
    const newUser = {
      username: 'test',
      email: '<EMAIL>',
      role: 'user'
    }
    const res = await createUser(newUser)
    console.log('用户创建成功:', res.data)
  } catch (error) {
    console.error('创建失败:', error)
  }
}
```

## 🔄 开发模式切换

### Mock 模式 → API 模式

1. 修改 `.env.base` 文件：
   ```env
   VITE_USE_MOCK=false
   ```

2. 重启前端开发服务器

### API 模式 → Mock 模式

1. 修改 `.env.base` 文件：
   ```env
   VITE_USE_MOCK=true
   ```

2. 重启前端开发服务器

## 🛠️ 扩展开发

### 添加新的API接口

1. **后端**: 在 `server/index.js` 中添加新路由
2. **前端**: 在 `src/api/hotel/index.ts` 中添加对应的服务函数
3. **页面**: 在组件中调用新的API服务

### 数据库集成

```bash
# MongoDB
npm install mongoose

# MySQL
npm install mysql2 sequelize
```

### 身份验证

```bash
npm install jsonwebtoken bcryptjs
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**
   - 后端默认端口: 8000
   - 前端默认端口: 4000
   - 可在启动时指定其他端口

2. **CORS 错误**
   - 确保后端已启动
   - 检查 CORS 配置

3. **API 请求失败**
   - 检查网络连接
   - 查看浏览器控制台错误
   - 确认API路径正确

### 调试技巧

1. **查看后端日志**
   ```bash
   # 后端控制台会显示所有请求
   ```

2. **查看前端网络请求**
   - 打开浏览器开发者工具
   - 查看 Network 面板

3. **测试API接口**
   ```bash
   # 使用 curl 测试
   curl http://localhost:8000/health
   curl http://localhost:8000/hotel/info
   ```

## 📞 技术支持

如果遇到问题，请检查：

1. Node.js 版本 >= 14.18.0
2. 所有依赖是否正确安装
3. 端口是否被占用
4. 防火墙设置

## 🎉 总结

现在您已经拥有了一个完整的 Vue3 + Node.js 全栈应用！可以基于这个基础继续开发更复杂的功能。
