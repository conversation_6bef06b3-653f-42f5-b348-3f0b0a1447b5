<template>
  <ContentWrap>
    <div class="hotel-info-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <div class="step-indicator">
              <span class="step-number">1</span>
              <div class="step-info">
                <h1 class="page-title">酒店信息管理</h1>
                <p class="page-description">完善酒店基础信息、预算规划和独特卖点</p>
              </div>
            </div>
          </div>

          <div class="header-right">
            <div class="status-indicator">
              <el-tag v-if="isApiMode" type="success" size="large" effect="dark">
                ☁️ API模式
              </el-tag>
              <el-tag v-else type="info" size="large" effect="dark">
                📄 本地模式
              </el-tag>
            </div>

            <div class="action-buttons">
              <el-button type="primary" :loading="loading" @click="loadFromApi" size="large">
                🔄 从API加载
              </el-button>
              <el-button type="success" :loading="loading" @click="saveAllData" size="large">
                ✅ 保存所有数据
              </el-button>
              <el-button type="warning" @click="resetForm" size="large">
                🔄 重置表单
              </el-button>
            </div>
          </div>
        </div>
      </div>



      <!-- 酒店基本信息表单 -->
      <div class="form-card">
        <div class="form-header">
          <div class="form-title">
            <span class="title-icon">🏨</span>
            <h3>酒店基本信息</h3>
          </div>
          <div class="form-subtitle">请填写酒店的基础信息，带*号为必填项</div>
        </div>

        <el-form ref="basicFormRef" :model="{ hotelBasicInfo }" label-width="140px" class="modern-form">
          <el-row :gutter="24">
            <el-col :xs="24" :sm="12" :lg="8" v-for="item in hotelBasicInfo" :key="item.label">
              <el-form-item :label="item.label" class="form-item">
                <el-input
                  v-model="item.value"
                  :placeholder="`请输入${item.label}`"
                  clearable
                  size="large"
                />
                <div class="field-note">{{ item.note }}</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 酒店2025年市场活动预算表单 -->
      <div class="form-card budget-card">
        <div class="form-header budget-header">
          <div class="form-title">
            <span class="title-icon budget-icon">💰</span>
            <h3>酒店2025年市场活动预算</h3>
          </div>
          <div class="form-subtitle">请填写各项预算金额，单位：人民币（元）</div>
        </div>

        <el-form ref="budgetFormRef" :model="{ budgetInfo }" label-width="200px" class="modern-form budget-form">
          <!-- 预算总览卡片 -->
          <div class="budget-overview">
            <div class="budget-summary-card">
              <div class="summary-icon">📊</div>
              <div class="summary-content">
                <h4>预算总览</h4>
                <p class="summary-description">2025年市场活动预算分配</p>
              </div>
            </div>
          </div>

          <!-- 预算项目网格 -->
          <el-row :gutter="20" class="budget-grid">
            <el-col
              v-for="(item, index) in budgetInfo"
              :key="item.label"
              :xs="24" :sm="24" :md="12" :lg="12" :xl="12"
              class="budget-item-col"
            >
              <div
                class="budget-item-card"
                :class="`budget-item-${index + 1}`"
              >
              <div class="budget-item-header">
                <div class="budget-item-icon">
                  <span v-if="index === 0">🏨</span>
                  <span v-else-if="index === 1">🤝</span>
                  <span v-else-if="index === 2">📈</span>
                  <span v-else>💎</span>
                </div>
                <div class="budget-item-title">
                  <h5>{{ item.label }}</h5>
                  <p v-if="index === 0" class="item-description">酒店本地市场推广活动</p>
                  <p v-else-if="index === 1" class="item-description">集团共享营销资源</p>
                  <p v-else-if="index === 2" class="item-description">品牌营销推广计划</p>
                  <p v-else class="item-description">总体预算规划</p>
                </div>
              </div>

              <div class="budget-input-container">
                <el-form-item class="budget-form-item">
                  <el-input
                    v-model="item.value"
                    :placeholder="`请输入${item.label}`"
                    clearable
                    size="large"
                    class="budget-input-field"
                  >
                    <template #prefix>
                      <span class="currency-symbol">¥</span>
                    </template>
                  </el-input>
                </el-form-item>
              </div>

              <!-- 预算进度指示器 -->
              <div class="budget-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: `${Math.min((parseFloat(item.value.replace(/[¥,]/g, '')) / 1000000) * 100, 100)}%` }"
                  ></div>
                </div>
                <span class="progress-text">
                  {{ (parseFloat(item.value.replace(/[¥,]/g, '')) / 10000).toFixed(1) }}万元
                </span>
              </div>
            </div>
            </el-col>
          </el-row>

          <!-- 预算统计信息 -->
          <div class="budget-statistics">
            <div class="stat-item">
              <div class="stat-icon">📊</div>
              <div class="stat-content">
                <span class="stat-label">预算项目</span>
                <span class="stat-value">{{ budgetInfo.length }}</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">💰</div>
              <div class="stat-content">
                <span class="stat-label">总预算</span>
                <span class="stat-value">{{ budgetInfo[budgetInfo.length - 1]?.value || '¥0' }}</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">📈</div>
              <div class="stat-content">
                <span class="stat-label">预算状态</span>
                <span class="stat-value status-active">已规划</span>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <!-- 酒店USP表单 - 重构为表格化设计 -->
      <div class="form-card usp-main-card">
        <div class="form-header">
          <div class="form-title">
            <span class="title-icon">⭐</span>
            <h3>酒店USP（独特卖点）</h3>
          </div>
          <div class="form-subtitle">
            核心卖点（USPs）是帮助客人与竞争对手区分开的关键，强调酒店特色与价值。请在表格中填写具体信息。
          </div>
        </div>

        <el-form ref="uspFormRef" :model="uspFormData" :rules="uspFormRules" class="modern-form usp-form">
          <!-- 客房特色 + 餐饮特色 合并子卡片 -->
          <div class="usp-sub-card">
            <div class="usp-sub-header">
              <div class="usp-sub-title">
                <span class="usp-sub-icon">🏨</span>
                <h4>客房与餐饮特色</h4>
              </div>
            </div>

            <div class="usp-tables-container">
              <!-- 客房特色表格 -->
              <div class="usp-table-section">
                <div class="usp-table-title">
                  <span class="table-icon">🛏️</span>
                  <span>客房特色</span>
                  <el-button
                    type="primary"
                    size="small"
                    @click="addUspRow('rooms')"
                    class="add-row-btn"
                  >
                    ➕ 新增行
                  </el-button>
                </div>
                <el-table
                  :data="uspFormData.rooms"
                  class="usp-table"
                  :show-header="true"
                  border
                >
                  <el-table-column label="特色项目" width="200">
                    <template #default="{ row, $index }">
                      <el-input
                        v-model="row.name"
                        placeholder="请输入特色项目"
                        size="small"
                        class="table-input"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="描述">
                    <template #default="{ row, $index }">
                      <el-input
                        v-model="row.description"
                        placeholder="请输入详细描述"
                        size="small"
                        class="table-input"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template #default="{ row, $index }">
                      <el-button
                        type="danger"
                        size="small"
                        @click="removeUspRow('rooms', $index)"
                        class="remove-btn"
                      >
                        🗑️
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 餐饮特色表格 -->
              <div class="usp-table-section">
                <div class="usp-table-title">
                  <span class="table-icon">🍽️</span>
                  <span>餐饮特色</span>
                  <el-button
                    type="primary"
                    size="small"
                    @click="addUspRow('dining')"
                    class="add-row-btn"
                  >
                    ➕ 新增行
                  </el-button>
                </div>
                <el-table
                  :data="uspFormData.dining"
                  class="usp-table"
                  :show-header="true"
                  border
                >
                  <el-table-column label="特色项目" width="200">
                    <template #default="{ row, $index }">
                      <el-input
                        v-model="row.name"
                        placeholder="请输入特色项目"
                        size="small"
                        class="table-input"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="描述">
                    <template #default="{ row, $index }">
                      <el-input
                        v-model="row.description"
                        placeholder="请输入详细描述"
                        size="small"
                        class="table-input"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template #default="{ row, $index }">
                      <el-button
                        type="danger"
                        size="small"
                        @click="removeUspRow('dining', $index)"
                        class="remove-btn"
                      >
                        🗑️
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>

          <!-- 会议及宴会特色 独立子卡片 -->
          <div class="usp-sub-card">
            <div class="usp-sub-header">
              <div class="usp-sub-title">
                <span class="usp-sub-icon">🎪</span>
                <h4>会议及宴会特色</h4>
              </div>
            </div>

            <div class="usp-table-section">
              <div class="usp-table-title">
                <span class="table-icon">🏛️</span>
                <span>会议及宴会设施</span>
                <el-button
                  type="primary"
                  size="small"
                  @click="addUspRow('meeting')"
                  class="add-row-btn"
                >
                  ➕ 新增行
                </el-button>
              </div>
              <el-table
                :data="uspFormData.meeting"
                class="usp-table"
                :show-header="true"
                border
              >
                <el-table-column label="特色项目" width="200">
                  <template #default="{ row, $index }">
                    <el-input
                      v-model="row.name"
                      placeholder="请输入特色项目"
                      size="small"
                      class="table-input"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="描述">
                  <template #default="{ row, $index }">
                    <el-input
                      v-model="row.description"
                      placeholder="请输入详细描述"
                      size="small"
                      class="table-input"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ row, $index }">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeUspRow('meeting', $index)"
                      class="remove-btn"
                    >
                      🗑️
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 其他服务特色 独立子卡片 -->
          <div class="usp-sub-card">
            <div class="usp-sub-header">
              <div class="usp-sub-title">
                <span class="usp-sub-icon">🌟</span>
                <h4>其他服务特色</h4>
              </div>
            </div>

            <div class="usp-table-section">
              <div class="usp-table-title">
                <span class="table-icon">🎯</span>
                <span>其他特色服务</span>
                <el-button
                  type="primary"
                  size="small"
                  @click="addUspRow('services')"
                  class="add-row-btn"
                >
                  ➕ 新增行
                </el-button>
              </div>
              <el-table
                :data="uspFormData.services"
                class="usp-table"
                :show-header="true"
                border
              >
                <el-table-column label="特色项目" width="200">
                  <template #default="{ row, $index }">
                    <el-input
                      v-model="row.name"
                      placeholder="请输入特色项目"
                      size="small"
                      class="table-input"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="描述">
                  <template #default="{ row, $index }">
                    <el-input
                      v-model="row.description"
                      placeholder="请输入详细描述"
                      size="small"
                      class="table-input"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80">
                  <template #default="{ row, $index }">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeUspRow('services', $index)"
                      class="remove-btn"
                    >
                      🗑️
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
// 暂时注释图标导入，使用文字图标
// import {
//   House as Building,
//   Coin as Money,
//   StarFilled as Star,
//   Upload as CloudUpload,
//   Document,
//   Refresh,
//   Check,
//   RefreshLeft
// } from '@element-plus/icons-vue'
import { ContentWrap } from '@/components/ContentWrap'
import { getHotelInfo, updateHotelInfo } from '@/api/hotel'

// 表单引用
const basicFormRef = ref()
const budgetFormRef = ref()
const uspFormRef = ref()

// 数据加载状态
const loading = ref(false)
const isApiMode = ref(false)

// USP表单数据 - 重构为表格数据结构
const uspFormData = reactive({
  rooms: [
    { name: '家庭房', description: '适合家庭入住的宽敞客房' },
    { name: '景观房', description: '拥有优美景观的客房' },
    { name: '独特风格房型', description: '具有特色设计的房型' }
  ],
  dining: [
    { name: '免费早餐', description: '提供丰富的免费早餐' },
    { name: '餐厅拥有佳景', description: '餐厅享有优美景观' },
    { name: '国际美食', description: '提供多样化国际美食' }
  ],
  meeting: [
    { name: '1000平米无柱宴会厅', description: '大型无柱宴会厅，适合各种活动' },
    { name: '40平米高清LED', description: '配备高清LED显示设备' },
    { name: '10种会议室组合', description: '多样化会议室配置' }
  ],
  services: [
    { name: '室外泳池/儿童泳池', description: '提供成人和儿童泳池设施' },
    { name: 'SPA', description: '专业SPA服务' },
    { name: '运动中心、健身房', description: '完善的健身运动设施' }
  ]
})

// 表单验证规则（USP表单使用）
const uspFormRules = reactive({
  rooms: [
    { required: false, message: '请输入客房特色', trigger: 'blur' }
  ],
  dining: [
    { required: false, message: '请输入餐饮特色', trigger: 'blur' }
  ],
  meeting: [
    { required: false, message: '请输入会议及宴会特色', trigger: 'blur' }
  ],
  services: [
    { required: false, message: '请输入其他服务特色', trigger: 'blur' }
  ]
})

// 酒店基本信息
const hotelBasicInfo = ref([
  { label: '计划制定人', value: 'Alice', note: '用户手填' },
  { label: '酒店In Code', value: 'AOGCN', note: 'incode预处理数据' },
  { label: '酒店名称', value: '九寨沟康莱德酒店', note: '预填与incode匹配' },
  { label: '酒店所在区域', value: '西区', note: '预填与incode匹配' },
  { label: '总经理', value: 'A', note: '用户手填' },
  { label: '商务总监', value: 'B', note: '用户手填' },
  { label: '市场总监', value: 'C', note: '用户手填' },
  { label: 'MECC 联系人', value: 'D', note: '用户手填' }
])

// 预算信息
const budgetInfo = ref([
  { label: '酒店本地活动预算', value: '¥620,125.00' },
  { label: '集团市场共享费（Co-op Fund）', value: '¥349,561.33' },
  { label: 'PMP', value: '¥60,000.00' },
  { label: '总预算', value: '¥1,029,686.33' }
])

// USP数据
const uspData = ref([
  {
    rooms: ['家庭房', '景观房', '独特风格房型', '亲子房'],
    dining: ['免费早餐', '餐厅拥有佳景', '国际美食'],
    meeting: ['1000平米无柱宴会厅', '40平米高清LED', '10种会议室组合'],
    services: ['室外泳池/儿童泳池', 'SPA', '运动中心、健身房']
  }
])

// 从API加载数据
const loadFromApi = async () => {
  loading.value = true
  try {
    const res = await getHotelInfo()
    console.log('🔍 API Response:', res)

    // 检查响应结构
    if (!res || !res.data) {
      throw new Error('API响应格式错误：缺少data字段')
    }

    const data = res.data
    console.log('📊 API Data:', data)

    // 检查数据字段
    if (!data.basicInfo || !data.budgetInfo || !data.uspData) {
      throw new Error('API数据格式错误：缺少必要字段')
    }

    hotelBasicInfo.value = data.basicInfo
    budgetInfo.value = data.budgetInfo
    uspData.value = data.uspData

    // 更新USP表单数据
    updateUspFormData()

    isApiMode.value = true
    ElMessage.success('数据已从API加载')
  } catch (error) {
    ElMessage.error('API加载失败，使用本地数据')
    console.error('Load from API failed:', error)
  } finally {
    loading.value = false
  }
}

// 更新USP表单数据 - 支持表格数据结构
const updateUspFormData = () => {
  if (uspData.value && uspData.value.length > 0) {
    const usp = uspData.value[0]

    // 转换数组数据为表格数据结构
    uspFormData.rooms = usp.rooms ? usp.rooms.map((item: string) => ({
      name: item,
      description: getDefaultDescription(item, 'rooms')
    })) : [{ name: '', description: '' }]

    uspFormData.dining = usp.dining ? usp.dining.map((item: string) => ({
      name: item,
      description: getDefaultDescription(item, 'dining')
    })) : [{ name: '', description: '' }]

    uspFormData.meeting = usp.meeting ? usp.meeting.map((item: string) => ({
      name: item,
      description: getDefaultDescription(item, 'meeting')
    })) : [{ name: '', description: '' }]

    uspFormData.services = usp.services ? usp.services.map((item: string) => ({
      name: item,
      description: getDefaultDescription(item, 'services')
    })) : [{ name: '', description: '' }]
  }
}

// 获取默认描述信息
const getDefaultDescription = (name: string, category: string): string => {
  const descriptions: Record<string, Record<string, string>> = {
    rooms: {
      '家庭房': '适合家庭入住的宽敞客房',
      '景观房': '拥有优美景观的客房',
      '独特风格房型': '具有特色设计的房型',
      '亲子房': '专为亲子家庭设计的客房'
    },
    dining: {
      '免费早餐': '提供丰富的免费早餐',
      '餐厅拥有佳景': '餐厅享有优美景观',
      '国际美食': '提供多样化国际美食'
    },
    meeting: {
      '1000平米无柱宴会厅': '大型无柱宴会厅，适合各种活动',
      '40平米高清LED': '配备高清LED显示设备',
      '10种会议室组合': '多样化会议室配置'
    },
    services: {
      '室外泳池/儿童泳池': '提供成人和儿童泳池设施',
      'SPA': '专业SPA服务',
      '运动中心、健身房': '完善的健身运动设施'
    }
  }

  return descriptions[category]?.[name] || '请输入详细描述'
}

// 保存所有数据
const saveAllData = async () => {
  loading.value = true
  try {
    // 验证USP表单
    const uspValid = await validateForm(uspFormRef.value)
    if (!uspValid) {
      ElMessage.warning('请检查表单填写是否正确')
      return
    }

    // 更新USP数据结构
    updateUspDataFromForm()

    // 使用统一的API保存所有数据
    const allData = {
      basicInfo: hotelBasicInfo.value,
      budgetInfo: budgetInfo.value,
      uspData: uspData.value
    }

    console.log('💾 保存数据:', allData)
    await updateHotelInfo(allData)

    ElMessage.success('所有数据已保存成功')
    isApiMode.value = true
  } catch (error) {
    ElMessage.error('保存数据失败')
    console.error('Save data failed:', error)
  } finally {
    loading.value = false
  }
}

// 表单验证辅助函数
const validateForm = async (formRef: any): Promise<boolean> => {
  if (!formRef) return true

  try {
    await formRef.validate()
    return true
  } catch (error) {
    console.log('Form validation failed:', error)
    return false
  }
}

// 从表单更新USP数据 - 支持表格数据结构
const updateUspDataFromForm = () => {
  uspData.value = [{
    rooms: uspFormData.rooms.filter(item => item.name.trim()).map(item => item.name),
    dining: uspFormData.dining.filter(item => item.name.trim()).map(item => item.name),
    meeting: uspFormData.meeting.filter(item => item.name.trim()).map(item => item.name),
    services: uspFormData.services.filter(item => item.name.trim()).map(item => item.name)
  }]
}

// 添加USP表格行
const addUspRow = (category: 'rooms' | 'dining' | 'meeting' | 'services') => {
  uspFormData[category].push({ name: '', description: '' })
}

// 删除USP表格行
const removeUspRow = (category: 'rooms' | 'dining' | 'meeting' | 'services', index: number) => {
  if (uspFormData[category].length > 1) {
    uspFormData[category].splice(index, 1)
  } else {
    ElMessage.warning('至少保留一行数据')
  }
}

// 重置表单
const resetForm = () => {
  // 重置表单验证状态
  basicFormRef.value?.clearValidate()
  budgetFormRef.value?.clearValidate()
  uspFormRef.value?.clearValidate()

  // 重置为初始数据
  hotelBasicInfo.value = [
    { label: '计划制定人', value: 'Alice', note: '用户手填' },
    { label: '酒店In Code', value: 'AOGCN', note: 'incode预处理数据' },
    { label: '酒店名称', value: '九寨沟康莱德酒店', note: '预填与incode匹配' },
    { label: '酒店所在区域', value: '西区', note: '预填与incode匹配' },
    { label: '总经理', value: 'A', note: '用户手填' },
    { label: '商务总监', value: 'B', note: '用户手填' },
    { label: '市场总监', value: 'C', note: '用户手填' },
    { label: 'MECC 联系人', value: 'D', note: '用户手填' }
  ]

  budgetInfo.value = [
    { label: '酒店本地活动预算', value: '¥620,125.00' },
    { label: '集团市场共享费（Co-op Fund）', value: '¥349,561.33' },
    { label: 'PMP', value: '¥60,000.00' },
    { label: '总预算', value: '¥1,029,686.33' }
  ]

  uspData.value = [{
    rooms: ['家庭房', '景观房', '独特风格房型', '亲子房'],
    dining: ['免费早餐', '餐厅拥有佳景', '国际美食'],
    meeting: ['1000平米无柱宴会厅', '40平米高清LED', '10种会议室组合'],
    services: ['室外泳池/儿童泳池', 'SPA', '运动中心、健身房']
  }]

  updateUspFormData()
  isApiMode.value = false
  ElMessage.success('表单已重置')
}

// 调试信息
onMounted(() => {
  console.log('🏨 酒店基本信息:', hotelBasicInfo.value)
  console.log('💰 预算信息:', budgetInfo.value)
  console.log('⭐ USP数据:', uspData.value)

  // 初始化USP表单数据
  updateUspFormData()
})
</script>

<style scoped>
/* Hilton Brand Color Variables - Pure Color Scheme */
:root {
  /* Primary Hilton Brand Colors */
  --hilton-navy: #001137;
  --hilton-blue: #002F61;
  --hilton-gold: #D4AF37;
  --hilton-light-blue: #6B8BC3;
  --hilton-white: #FFFFFF;

  /* Extended Hilton Color Palette */
  --hilton-blue-shade: #001137;
  --hilton-blue-tint: #68688C;
  --hilton-turquoise-shade: #005670;
  --hilton-turquoise: #007293;
  --hilton-turquoise-tint: #799CB6;
  --hilton-teal-shade: #00614F;
  --hilton-teal: #06937E;
  --hilton-teal-tint: #6FB3A7;

  /* Text Colors */
  --hilton-text-dark: #1A1A1A;
  --hilton-text-light: #6B7280;
  --hilton-text-on-dark: #FFFFFF;

  /* Glassmorphism Variables */
  --glass-bg-light: rgba(255, 255, 255, 0.25);
  --glass-bg-medium: rgba(255, 255, 255, 0.15);
  --glass-bg-dark: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: rgba(0, 17, 55, 0.1);

  /* Background Variations */
  --hilton-navy-transparent: rgba(0, 17, 55, 0.9);
  --hilton-blue-transparent: rgba(0, 47, 97, 0.9);
  --hilton-gold-transparent: rgba(212, 175, 55, 0.1);
}

.hotel-info-container {
  font-family: 'Inter', 'Segoe UI', 'Helvetica Neue', sans-serif;
  background: var(--hilton-navy);
  min-height: 100vh;
  padding: 0;
  position: relative;
  overflow-x: hidden;
}

/* 希尔顿金色装饰元素 */
.hotel-info-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--hilton-gold);
  z-index: 100;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

/* 侧边金色装饰条 */
.hotel-info-container::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background:
    linear-gradient(180deg,
      var(--hilton-gold) 0%,
      var(--hilton-turquoise) 50%,
      var(--hilton-teal) 100%
    );
  z-index: 99;
  box-shadow: 2px 0 8px rgba(212, 175, 55, 0.2);
}

/* 页面头部样式 - 标准希尔顿蓝色背景 */
.page-header {
  background: #001137 !important;
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  color: #FFFFFF !important;
  padding: 48px 32px;
  margin: -20px -20px 40px -20px;
  border-radius: 0 0 40px 40px;
  border: 3px solid #D4AF37;
  border-top: none;
  box-shadow:
    0 12px 40px rgba(0, 17, 55, 0.5),
    0 6px 20px rgba(0, 17, 55, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  z-index: 10;
}

/* 头部金色装饰元素 */
.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #D4AF37;
  z-index: 3;
  box-shadow: 0 2px 6px rgba(212, 175, 55, 0.5);
}

/* 头部右上角装饰 */
.page-header::after {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #007293;
  border-radius: 50%;
  opacity: 0.15;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  gap: 32px;
  position: relative;
  z-index: 1;
}

.header-left {
  flex: 1;
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: 24px;
}

.step-number {
  width: 70px;
  height: 70px;
  background: #D4AF37;
  border: 3px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: 800;
  color: #001137;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 24px rgba(212, 175, 55, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 2px 4px rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.step-number:hover {
  transform: scale(1.05);
  box-shadow:
    0 12px 32px rgba(212, 175, 55, 0.4),
    0 6px 16px rgba(0, 0, 0, 0.3),
    inset 0 2px 4px rgba(255, 255, 255, 0.4);
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #FFFFFF !important;
  text-shadow:
    0 2px 8px rgba(0, 0, 0, 0.5),
    0 1px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.page-description {
  font-size: 18px;
  margin: 0;
  color: #FFFFFF !important;
  opacity: 0.95;
  font-weight: 400;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.5;
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 20px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.status-indicator {
  margin-bottom: 12px;
}

/* 表单卡片样式 - Pure White with Hilton Accents */
.form-card {
  background: var(--hilton-white);
  backdrop-filter: blur(25px) saturate(180%);
  -webkit-backdrop-filter: blur(25px) saturate(180%);
  border-radius: 28px;
  box-shadow:
    0 16px 50px rgba(0, 17, 55, 0.2),
    0 8px 25px rgba(0, 17, 55, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 36px;
  overflow: hidden;
  border: 2px solid var(--hilton-turquoise-tint);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 5;
}

/* 卡片顶部希尔顿金色装饰条 */
.form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--hilton-gold);
  z-index: 3;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.4);
}

/* 卡片左侧装饰条 */
.form-card::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 0;
  width: 4px;
  height: calc(100% - 6px);
  background: var(--hilton-turquoise);
  z-index: 2;
}

.form-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 25px 70px rgba(0, 17, 55, 0.25),
    0 12px 35px rgba(0, 17, 55, 0.15),
    0 6px 20px rgba(212, 175, 55, 0.1);
  border-color: var(--hilton-gold);
}

.form-header {
  background: var(--hilton-light-blue);
  backdrop-filter: blur(15px) saturate(150%);
  -webkit-backdrop-filter: blur(15px) saturate(150%);
  padding: 28px 32px 22px;
  border-bottom: 3px solid var(--hilton-teal);
  position: relative;
  border-radius: 28px 28px 0 0;
}

/* 表单头部右上角装饰 */
.form-header::before {
  content: '';
  position: absolute;
  top: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  background: var(--hilton-gold);
  border-radius: 50%;
  opacity: 0.2;
  z-index: 1;
}

/* 表单头部底部装饰线 */
.form-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 44px;
  right: 44px;
  height: 3px;
  background: var(--hilton-gold);
  border-radius: 1px;
  box-shadow: 0 1px 4px rgba(212, 175, 55, 0.4);
  z-index: 2;
}

.form-title {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.title-icon {
  font-size: 28px;
  margin-right: 4px;
  display: inline-block;
  filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
  transition: all 0.3s ease;
}

.form-card:hover .title-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 4px 8px rgba(212, 175, 55, 0.4));
}

.form-title h3 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: var(--hilton-navy);
  letter-spacing: -0.3px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-subtitle {
  color: var(--hilton-text-light);
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
}

.modern-form {
  padding: 28px;
  position: relative;
}

.form-item {
  margin-bottom: 24px;
  position: relative;
}

.field-note {
  font-size: 13px;
  color: var(--hilton-text-light);
  margin-top: 8px;
  font-style: italic;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.form-item:hover .field-note {
  opacity: 1;
}

/* 预算卡片专用样式 - Pure Color Budget Design */
.budget-card {
  background: var(--hilton-white);
  border: 3px solid var(--hilton-gold);
  box-shadow:
    0 20px 60px rgba(212, 175, 55, 0.15),
    0 10px 30px rgba(0, 17, 55, 0.1);
}

.budget-card::before {
  background: var(--hilton-gold);
  height: 8px;
  box-shadow: 0 3px 12px rgba(212, 175, 55, 0.5);
}

.budget-card::after {
  background: var(--hilton-teal);
  width: 6px;
}

.budget-header {
  background: var(--hilton-turquoise-tint);
  border-bottom: 4px solid var(--hilton-gold);
}

.budget-icon {
  background: linear-gradient(135deg, var(--hilton-gold) 0%, #E6C547 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.4));
}

.budget-form {
  padding: 32px 28px;
}

/* 预算总览卡片 */
.budget-overview {
  margin-bottom: 40px;
}

.budget-summary-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px 32px;
  background: var(--hilton-white);
  backdrop-filter: blur(15px) saturate(150%);
  -webkit-backdrop-filter: blur(15px) saturate(150%);
  border-radius: 20px;
  border: 2px solid var(--hilton-turquoise);
  box-shadow:
    0 8px 24px rgba(0, 114, 147, 0.15),
    0 4px 12px rgba(0, 17, 55, 0.08);
}

.summary-icon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.summary-content h4 {
  font-size: 20px;
  font-weight: 700;
  color: var(--hilton-navy);
  margin: 0 0 4px 0;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.summary-description {
  font-size: 14px;
  color: var(--hilton-text-light);
  margin: 0;
  opacity: 0.9;
}

/* 预算网格布局 - 两列布局 */
.budget-grid {
  margin-bottom: 32px;
}

.budget-item-col {
  margin-bottom: 20px;
}

/* 预算项目卡片 - 紧凑样式 */
.budget-item-card {
  background: var(--hilton-white);
  border-radius: 14px;
  border: 2px solid var(--hilton-teal-tint);
  padding: 18px;
  position: relative;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.budget-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--hilton-gold);
  border-radius: 16px 16px 0 0;
}

/* 预算项目头部 */
.budget-item-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 18px;
}

.budget-item-icon {
  width: 48px;
  height: 48px;
  background: var(--hilton-turquoise);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  border: 2px solid var(--hilton-gold);
  flex-shrink: 0;
}

.budget-item-title h5 {
  font-size: 18px;
  font-weight: 700;
  color: var(--hilton-navy);
  margin: 0 0 6px 0;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.item-description {
  font-size: 13px;
  color: var(--hilton-text-light);
  margin: 0;
  opacity: 0.8;
  line-height: 1.4;
}

/* 预算输入容器 - 紧凑样式 */
.budget-input-container {
  margin-bottom: 16px;
  width: 100%;
}

.budget-form-item {
  margin-bottom: 0;
  width: 100%;
}

/* 预算输入框样式 - 与市场总监输入框保持一致 */
.budget-input-field {
  width: 100%;
  display: block;
}

/* 移除预算输入框的特殊样式，使用通用样式 */
.budget-input-field :deep(.el-input__wrapper) {
  padding-left: 40px;
  min-width: 300px;
}

.budget-input-field :deep(.el-input__prefix) {
  left: 12px;
  display: flex;
  align-items: center;
  position: absolute;
}

.currency-symbol {
  color: var(--hilton-gold);
  font-weight: 700;
  font-size: 16px;
  margin-right: 6px;
}

/* 预算进度条 */
.budget-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background:
    linear-gradient(135deg,
      rgba(0, 17, 55, 0.1) 0%,
      rgba(107, 139, 195, 0.1) 100%
    );
  border-radius: 6px;
  overflow: hidden;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 17, 55, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 17, 55, 0.1);
}

.progress-fill {
  height: 100%;
  background:
    linear-gradient(90deg,
      var(--hilton-gold) 0%,
      #E6C547 50%,
      var(--hilton-navy) 100%
    );
  border-radius: 6px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 1px 3px rgba(212, 175, 55, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.3) 50%,
      transparent 100%
    );
  animation: shimmer 2s ease-in-out infinite;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--hilton-navy);
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 预算统计信息 */
.budget-statistics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 32px;
  background: var(--hilton-light-blue);
  backdrop-filter: blur(15px) saturate(150%);
  -webkit-backdrop-filter: blur(15px) saturate(150%);
  border-radius: 20px;
  border: 3px solid var(--hilton-teal);
  box-shadow:
    0 8px 24px rgba(107, 139, 195, 0.15),
    0 4px 12px rgba(0, 17, 55, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--hilton-white);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 2px solid var(--hilton-turquoise-tint);
  transition: all 0.3s ease;
  box-shadow:
    0 4px 12px rgba(0, 17, 55, 0.08),
    0 2px 6px rgba(121, 156, 182, 0.1);
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(0, 17, 55, 0.12),
    0 4px 10px rgba(121, 156, 182, 0.15);
  border-color: var(--hilton-gold);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: var(--hilton-teal);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  border: 2px solid var(--hilton-gold);
  box-shadow:
    0 2px 8px rgba(6, 147, 126, 0.2),
    0 1px 4px rgba(212, 175, 55, 0.15);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 13px;
  color: var(--hilton-text-light);
  font-weight: 500;
  opacity: 0.8;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--hilton-navy);
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.status-active {
  color: var(--hilton-gold);
  text-shadow: 0 1px 2px rgba(212, 175, 55, 0.3);
}

/* USP表格化设计样式 - 现代专业风格 */
.usp-main-card {
  border: 3px solid var(--hilton-turquoise);
  box-shadow:
    0 20px 60px rgba(0, 114, 147, 0.15),
    0 10px 30px rgba(0, 17, 55, 0.1);
}

.usp-main-card::before {
  background: var(--hilton-turquoise);
  height: 8px;
  box-shadow: 0 3px 12px rgba(0, 114, 147, 0.5);
}

.usp-main-card::after {
  background: var(--hilton-gold);
  width: 6px;
}

.usp-form {
  padding: 24px;
}

/* USP子卡片样式 */
.usp-sub-card {
  background: var(--hilton-white);
  border-radius: 18px;
  border: 2px solid var(--hilton-turquoise-tint);
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow:
    0 8px 24px rgba(0, 114, 147, 0.1),
    0 4px 12px rgba(0, 17, 55, 0.05);
  transition: all 0.3s ease;
}

.usp-sub-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 32px rgba(0, 114, 147, 0.15),
    0 6px 16px rgba(0, 17, 55, 0.08);
  border-color: var(--hilton-gold);
}

.usp-sub-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--hilton-turquoise);
  z-index: 2;
}

/* USP子卡片头部 */
.usp-sub-header {
  background: var(--hilton-light-blue);
  padding: 16px 20px;
  border-bottom: 2px solid var(--hilton-turquoise);
  position: relative;
}

.usp-sub-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.usp-sub-icon {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 114, 147, 0.3));
}

.usp-sub-title h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--hilton-navy);
  margin: 0;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 表格容器样式 */
.usp-tables-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
}

.usp-table-section {
  background: var(--hilton-white);
  border-radius: 16px;
  border: 1px solid rgba(0, 114, 147, 0.1);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 17, 55, 0.05);
}

/* 表格标题栏 */
.usp-table-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--hilton-blue);
  color: var(--hilton-white);
  font-weight: 600;
  font-size: 13px;
  border-bottom: 2px solid var(--hilton-gold);
}

.usp-table-title .table-icon {
  font-size: 16px;
  margin-right: 8px;
}

.add-row-btn {
  background: var(--hilton-gold) !important;
  border-color: var(--hilton-gold) !important;
  color: var(--hilton-navy) !important;
  font-size: 12px !important;
  padding: 6px 12px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
}

.add-row-btn:hover {
  background: #E6C547 !important;
  border-color: #E6C547 !important;
  transform: scale(1.05);
}

/* USP表格样式 */
.usp-table {
  width: 100%;
  font-size: 14px;
}

.usp-table :deep(.el-table__header) {
  background: var(--hilton-blue);
}

.usp-table :deep(.el-table__header th) {
  background: var(--hilton-blue) !important;
  color: var(--hilton-white) !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  padding: 12px 8px !important;
  border-bottom: 2px solid var(--hilton-gold) !important;
}

.usp-table :deep(.el-table__body tr) {
  transition: background-color 0.2s ease;
}

.usp-table :deep(.el-table__body tr:hover) {
  background: rgba(0, 114, 147, 0.05) !important;
}

.usp-table :deep(.el-table__body td) {
  padding: 8px !important;
  border-bottom: 1px solid rgba(0, 114, 147, 0.1) !important;
}

/* 表格内输入框样式 */
.table-input :deep(.el-input__wrapper) {
  border: 1px solid rgba(0, 114, 147, 0.2) !important;
  border-radius: 8px !important;
  padding: 6px 12px !important;
  background: var(--hilton-white) !important;
  font-size: 13px !important;
  transition: all 0.2s ease !important;
}

.table-input :deep(.el-input__wrapper):hover {
  border-color: var(--hilton-gold) !important;
  box-shadow: 0 0 0 1px rgba(212, 175, 55, 0.2) !important;
}

.table-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--hilton-turquoise) !important;
  box-shadow: 0 0 0 2px rgba(0, 114, 147, 0.2) !important;
}

.table-input :deep(.el-input__inner) {
  color: var(--hilton-text-dark) !important;
  font-size: 13px !important;
  font-weight: 500 !important;
}

/* 删除按钮样式 - 绿色主题 */
.remove-btn {
  background: #67C23A !important;
  border-color: #67C23A !important;
  color: white !important;
  font-size: 12px !important;
  padding: 6px 8px !important;
  border-radius: 6px !important;
  min-width: 32px !important;
}

.remove-btn:hover {
  background: #5daf34 !important;
  border-color: #5daf34 !important;
  transform: scale(1.05);
}

/* 独立子卡片样式调整 */
.usp-sub-card:last-child .usp-table-section {
  margin: 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .usp-tables-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .usp-form {
    padding: 24px;
  }

  .usp-sub-card {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .usp-form {
    padding: 20px;
  }

  .usp-sub-header {
    padding: 16px 20px;
  }

  .usp-sub-title h4 {
    font-size: 16px;
  }

  .usp-table-title {
    padding: 12px 16px;
    font-size: 13px;
  }

  .usp-tables-container {
    padding: 20px;
    gap: 16px;
  }

  .add-row-btn {
    font-size: 11px !important;
    padding: 4px 8px !important;
  }
}

/* Element Plus 组件样式覆盖 - 简化样式 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: var(--hilton-navy);
  font-size: 15px;
  margin-bottom: 10px;
}

/* 通用输入框样式 - 简化版本 */
:deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid rgba(0, 17, 55, 0.12);
  background: var(--hilton-white);
  padding: 8px 16px;
  transition: border-color 0.2s ease;
  box-shadow: none;
}

:deep(.el-input__wrapper):hover {
  border-color: rgba(212, 175, 55, 0.4);
  box-shadow: none;
  transform: none;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--hilton-gold);
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
  transform: none;
}

:deep(.el-input__inner) {
  color: var(--hilton-text-dark);
  font-weight: 500;
  font-size: 15px;
}

:deep(.el-button) {
  border-radius: 12px;
  font-weight: 600;
  padding: 12px 24px;
  font-size: 15px;
  transition: background-color 0.2s ease;
  box-shadow: none;
  border: 2px solid transparent;
}

:deep(.el-button:hover) {
  transform: none;
  box-shadow: none;
}

/* 头部按钮特殊样式 - 希尔顿品牌配色 */
.page-header :deep(.el-button--primary) {
  background: var(--hilton-turquoise);
  border-color: var(--hilton-turquoise);
  color: var(--hilton-white);
  font-weight: 700;
  box-shadow:
    0 4px 12px rgba(0, 114, 147, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

.page-header :deep(.el-button--primary:hover) {
  background: var(--hilton-turquoise-shade);
  border-color: var(--hilton-gold);
  box-shadow:
    0 6px 16px rgba(0, 114, 147, 0.4),
    0 3px 8px rgba(212, 175, 55, 0.2);
}

.page-header :deep(.el-button--success) {
  background: var(--hilton-gold);
  border-color: var(--hilton-gold);
  color: var(--hilton-navy);
  font-weight: 700;
  box-shadow:
    0 4px 12px rgba(212, 175, 55, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

.page-header :deep(.el-button--success:hover) {
  background: #E6C547;
  border-color: var(--hilton-turquoise);
  color: var(--hilton-navy);
  box-shadow:
    0 6px 16px rgba(230, 197, 71, 0.5),
    0 3px 8px rgba(0, 114, 147, 0.2);
}

.page-header :deep(.el-button--warning) {
  background: var(--hilton-teal);
  border-color: var(--hilton-teal);
  color: var(--hilton-white);
  font-weight: 700;
  box-shadow:
    0 4px 12px rgba(6, 147, 126, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

.page-header :deep(.el-button--warning:hover) {
  background: var(--hilton-teal-shade);
  border-color: var(--hilton-gold);
  box-shadow:
    0 6px 16px rgba(6, 147, 126, 0.4),
    0 3px 8px rgba(212, 175, 55, 0.2);
}

/* 通用按钮样式 */
:deep(.el-button--primary) {
  background: var(--hilton-blue);
  border-color: var(--hilton-blue);
  color: var(--hilton-white);
}

:deep(.el-button--primary:hover) {
  background: var(--hilton-navy);
  border-color: var(--hilton-gold);
}

:deep(.el-button--success) {
  background: var(--hilton-gold);
  border-color: var(--hilton-gold);
  color: var(--hilton-navy);
}

:deep(.el-button--success:hover) {
  background: #E6C547;
  border-color: var(--hilton-navy);
}

:deep(.el-button--warning) {
  background: var(--hilton-light-blue);
  border-color: var(--hilton-light-blue);
  color: var(--hilton-white);
}

:deep(.el-tag) {
  border-radius: 14px;
  font-weight: 600;
  padding: 10px 18px;
  font-size: 14px;
  letter-spacing: -0.1px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 6px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px) saturate(140%);
  -webkit-backdrop-filter: blur(8px) saturate(140%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 头部状态标签特殊样式 */
.page-header :deep(.el-tag--success) {
  background: var(--hilton-gold);
  border-color: var(--hilton-gold);
  color: var(--hilton-navy);
  font-weight: 700;
  font-size: 14px;
  padding: 8px 16px;
  box-shadow:
    0 3px 8px rgba(212, 175, 55, 0.3),
    0 1px 4px rgba(0, 0, 0, 0.1);
}

.page-header :deep(.el-tag--info) {
  background: var(--hilton-turquoise);
  border-color: var(--hilton-turquoise);
  color: var(--hilton-white);
  font-weight: 700;
  font-size: 14px;
  padding: 8px 16px;
  box-shadow:
    0 3px 8px rgba(0, 114, 147, 0.3),
    0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 通用标签样式 */
:deep(.el-tag--success) {
  background: var(--hilton-gold);
  border-color: var(--hilton-gold);
  color: var(--hilton-navy);
}

:deep(.el-tag--info) {
  background: var(--hilton-light-blue);
  border-color: var(--hilton-light-blue);
  color: var(--hilton-white);
}

/* 响应式设计 - Enhanced Glassmorphism for Multi-Resolution */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 32px;
  }

  .header-right {
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
    gap: 16px;
  }

  .form-card {
    margin-bottom: 28px;
    backdrop-filter: blur(20px) saturate(160%);
    -webkit-backdrop-filter: blur(20px) saturate(160%);
  }

  .modern-form {
    padding: 24px 20px;
  }

  .form-header {
    padding: 24px 28px 18px;
    backdrop-filter: blur(12px) saturate(140%);
    -webkit-backdrop-filter: blur(12px) saturate(140%);
  }

  .form-header::after {
    left: 36px;
    right: 36px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 36px 24px;
    margin: -20px -20px 32px -20px;
    border-radius: 0 0 28px 28px;
    backdrop-filter: blur(15px) saturate(160%);
    -webkit-backdrop-filter: blur(15px) saturate(160%);
  }

  .step-indicator {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .step-number {
    width: 64px;
    height: 64px;
    font-size: 26px;
    margin: 0 auto;
  }

  .page-title {
    font-size: 30px;
    text-align: center;
  }

  .page-description {
    font-size: 17px;
    text-align: center;
  }

  .modern-form {
    padding: 32px 24px;
  }

  .form-header {
    padding: 28px 24px;
    backdrop-filter: blur(10px) saturate(130%);
    -webkit-backdrop-filter: blur(10px) saturate(130%);
  }

  .form-header::after {
    left: 24px;
    right: 24px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 16px;
  }

  .action-buttons .el-button {
    width: 100%;
    padding: 16px 28px;
  }

  .form-card {
    border-radius: 24px;
    margin-bottom: 32px;
    backdrop-filter: blur(18px) saturate(150%);
    -webkit-backdrop-filter: blur(18px) saturate(150%);
  }

  /* 预算卡片响应式 */
  .budget-form {
    padding: 32px 24px;
  }

  .budget-grid {
    gap: 20px;
  }

  .budget-item-card {
    padding: 24px;
    width: 100%;
  }

  .budget-input-field :deep(.el-input__wrapper) {
    min-width: 250px;
    padding-left: 35px;
  }

  .budget-statistics {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 24px;
  }

  :deep(.el-input__wrapper) {
    backdrop-filter: blur(8px) saturate(130%);
    -webkit-backdrop-filter: blur(8px) saturate(130%);
  }

  :deep(.el-button) {
    backdrop-filter: blur(6px) saturate(120%);
    -webkit-backdrop-filter: blur(6px) saturate(120%);
  }
}

@media (max-width: 480px) {
  .hotel-info-container {
    padding: 0;
    background: var(--hilton-navy);
  }

  .hotel-info-container::before {
    width: 100%;
    height: 3px;
  }

  .hotel-info-container::after {
    width: 4px;
  }

  .page-header {
    margin: 0 0 24px 0;
    border-radius: 0 0 20px 20px;
    padding: 28px 20px;
    backdrop-filter: blur(12px) saturate(140%);
    -webkit-backdrop-filter: blur(12px) saturate(140%);
  }

  .step-number {
    width: 56px;
    height: 56px;
    font-size: 22px;
  }

  .page-title {
    font-size: 26px;
  }

  .page-description {
    font-size: 15px;
  }

  .form-card {
    margin: 0 0 24px 0;
    border-radius: 20px;
    backdrop-filter: blur(15px) saturate(140%);
    -webkit-backdrop-filter: blur(15px) saturate(140%);
  }

  .form-card::before {
    height: 4px;
  }

  .form-header {
    padding: 24px 20px;
    backdrop-filter: blur(8px) saturate(120%);
    -webkit-backdrop-filter: blur(8px) saturate(120%);
  }

  .form-header::after {
    left: 20px;
    right: 20px;
  }

  .modern-form {
    padding: 28px 20px;
  }

  .form-title h3 {
    font-size: 22px;
  }

  .title-icon {
    font-size: 26px;
  }

  .form-subtitle {
    font-size: 15px;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
  }

  :deep(.el-input__wrapper) {
    border-radius: 14px;
    padding: 4px 14px;
    backdrop-filter: blur(6px) saturate(120%);
    -webkit-backdrop-filter: blur(6px) saturate(120%);
  }

  :deep(.el-button) {
    border-radius: 14px;
    padding: 14px 22px;
    font-size: 14px;
    backdrop-filter: blur(5px) saturate(110%);
    -webkit-backdrop-filter: blur(5px) saturate(110%);
  }

  .usp-textarea :deep(.el-textarea__inner) {
    border-radius: 14px;
    padding: 14px;
    font-size: 14px;
    backdrop-filter: blur(6px) saturate(120%);
    -webkit-backdrop-filter: blur(6px) saturate(120%);
  }

  /* 移动端预算卡片优化 */
  .budget-form {
    padding: 24px 20px;
  }

  .budget-summary-card {
    padding: 20px 24px;
    gap: 16px;
  }

  .summary-icon {
    font-size: 28px;
  }

  .summary-content h4 {
    font-size: 18px;
  }

  .budget-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .budget-item-card {
    padding: 20px;
  }

  .budget-item-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .budget-item-title h5 {
    font-size: 16px;
  }

  .item-description {
    font-size: 12px;
  }

  /* 移动端预算输入框优化 */
  .budget-input-field {
    width: 100%;
  }

  .budget-input-field :deep(.el-input__wrapper) {
    width: 100%;
    min-width: 280px;
    padding-left: 35px;
  }

  .budget-input-field :deep(.el-input__prefix) {
    left: 10px;
  }

  .currency-symbol {
    font-size: 14px;
    margin-right: 4px;
  }

  .budget-statistics {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 20px;
  }

  .stat-item {
    padding: 16px;
  }

  .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 14px;
  }

  .progress-text {
    font-size: 11px;
  }
}

/* 额外的视觉增强 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.form-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(212, 175, 55, 0.03),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.form-card:hover::after {
  opacity: 1;
}

/* High-DPI and Performance Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .form-card {
    backdrop-filter: blur(20px) saturate(160%);
    -webkit-backdrop-filter: blur(20px) saturate(160%);
  }

  .page-header {
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
  }

  :deep(.el-input__wrapper) {
    backdrop-filter: blur(8px) saturate(130%);
    -webkit-backdrop-filter: blur(8px) saturate(130%);
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .form-card::after {
    animation: none;
  }

  .page-header::after {
    animation: none;
  }
}

/* Performance Optimizations */
.form-card,
.page-header,
:deep(.el-input__wrapper),
:deep(.el-button) {
  will-change: transform, backdrop-filter;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Enhanced Scrollbar with Glassmorphism */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 17, 55, 0.08);
  border-radius: 6px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

::-webkit-scrollbar-thumb {
  background:
    linear-gradient(135deg,
      rgba(212, 175, 55, 0.8) 0%,
      rgba(230, 197, 71, 0.8) 100%
    );
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px) saturate(150%);
  -webkit-backdrop-filter: blur(8px) saturate(150%);
}

::-webkit-scrollbar-thumb:hover {
  background:
    linear-gradient(135deg,
      rgba(230, 197, 71, 0.9) 0%,
      rgba(240, 208, 96, 0.9) 100%
    );
  backdrop-filter: blur(10px) saturate(170%);
  -webkit-backdrop-filter: blur(10px) saturate(170%);
}

/* Ultra-wide Screen Support */
@media (min-width: 1920px) {
  .hotel-info-container {
    background-attachment: fixed;
  }

  .form-card {
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
  }

  .page-header .header-content {
    max-width: 1600px;
  }
}
</style>
