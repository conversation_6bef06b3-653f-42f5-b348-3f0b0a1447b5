const initSqlJs = require('sql.js')
import { readFileSync, writeFileSync, existsSync } from 'fs'
import { join } from 'path'
import { app } from 'electron'

// SQLite数据库适配器
export class SQLiteAdapter {
  private SQL: any
  private db: any
  private dbPath: string

  constructor(dbPath?: string) {
    this.dbPath = dbPath || join(app.getPath('userData'), 'hiltonmarket.db')
  }

  // 初始化SQLite
  async init(): Promise<void> {
    try {
      console.log('🔄 正在初始化SQLite数据库...')
      const startTime = Date.now()

      // 初始化sql.js（这是最耗时的操作）
      console.log('📦 加载sql.js WebAssembly模块...')
      this.SQL = await initSqlJs({
        // 可以指定wasm文件路径，这里使用默认
      })
      console.log(`✅ sql.js加载完成 (${Date.now() - startTime}ms)`)

      // 加载或创建数据库
      if (existsSync(this.dbPath)) {
        console.log('📂 加载现有SQLite数据库文件')
        const filebuffer = readFileSync(this.dbPath)
        this.db = new this.SQL.Database(filebuffer)
      } else {
        console.log('🆕 创建新的SQLite数据库')
        this.db = new this.SQL.Database()
      }

      // 创建表结构
      this.createTables()

      console.log(`✅ SQLite数据库初始化完成 (总耗时: ${Date.now() - startTime}ms)`)
    } catch (error) {
      console.error('❌ SQLite初始化失败:', error)
      throw error
    }
  }

  // 创建表结构
  private async createTables(): Promise<void> {
    const tables = [
      // 用户表
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        role TEXT NOT NULL DEFAULT 'user',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 酒店基本信息表
      `CREATE TABLE IF NOT EXISTS hotel_basic_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        value TEXT NOT NULL,
        note TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 酒店预算信息表
      `CREATE TABLE IF NOT EXISTS hotel_budget_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        value TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // 酒店USP信息表
      `CREATE TABLE IF NOT EXISTS hotel_usp_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        items TEXT NOT NULL, -- JSON字符串
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ]

    for (const sql of tables) {
      this.db.run(sql)
    }
  }

  // 执行查询
  query(sql: string, params: any[] = []): any[] {
    try {
      const stmt = this.db.prepare(sql)
      const result = stmt.getAsObject(params)
      return Array.isArray(result) ? result : [result]
    } catch (error) {
      console.error('SQLite查询错误:', error)
      throw error
    }
  }

  // 执行查询并返回所有结果
  all(sql: string, params: any[] = []): any[] {
    try {
      const stmt = this.db.prepare(sql)
      const results: any[] = []

      while (stmt.step()) {
        results.push(stmt.getAsObject())
      }

      stmt.free()
      return results
    } catch (error) {
      console.error('SQLite查询错误:', error)
      throw error
    }
  }

  // 执行查询并返回第一个结果
  get(sql: string, params: any[] = []): any {
    const results = this.all(sql, params)
    return results.length > 0 ? results[0] : null
  }

  // 执行插入/更新/删除
  run(sql: string, params: any[] = []): { lastID: number; changes: number } {
    try {
      const stmt = this.db.prepare(sql)
      stmt.run(params)

      const result = {
        lastID: this.db.exec("SELECT last_insert_rowid() as id")[0]?.values[0]?.[0] || 0,
        changes: this.db.getRowsModified()
      }

      stmt.free()
      return result
    } catch (error) {
      console.error('SQLite执行错误:', error)
      throw error
    }
  }

  // 保存数据库到文件
  save(): void {
    try {
      const data = this.db.export()
      writeFileSync(this.dbPath, data)
      console.log('💾 SQLite数据库已保存到文件')
    } catch (error) {
      console.error('❌ 保存SQLite数据库失败:', error)
      throw error
    }
  }

  // 关闭数据库
  close(): void {
    try {
      if (this.db) {
        this.save() // 保存数据
        this.db.close()
        console.log('✅ SQLite数据库已关闭')
      }
    } catch (error) {
      console.error('❌ 关闭SQLite数据库失败:', error)
    }
  }

  // 初始化默认数据（优化版本）
  async initializeData(): Promise<void> {
    try {
      console.log('🔍 检查SQLite数据初始化状态...')
      const startTime = Date.now()

      // 检查是否已有数据
      const userCount = this.get('SELECT COUNT(*) as count FROM users')?.count || 0

      if (userCount === 0) {
        console.log('📝 开始初始化SQLite默认数据...')

        // 使用事务批量插入，提高性能
        this.db.exec('BEGIN TRANSACTION')

        try {
          // 批量插入默认用户
          const userInserts = [
            "INSERT INTO users (username, email, role) VALUES ('admin', '<EMAIL>', 'admin')",
            "INSERT INTO users (username, email, role) VALUES ('user', '<EMAIL>', 'user')"
          ]

          // 批量插入酒店基本信息
          const basicInfoInserts = [
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('计划制定人', 'Alice', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店In Code', 'AOGCN', 'incode预处理数据')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店名称', '九寨沟康莱德酒店', '预填与incode匹配')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店所在区域', '西区', '预填与incode匹配')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('总经理', 'A', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('商务总监', 'B', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('市场总监', 'C', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('MECC 联系人', 'D', '用户手填')"
          ]

          // 批量插入预算信息
          const budgetInfoInserts = [
            "INSERT INTO hotel_budget_info (label, value) VALUES ('酒店本地活动预算', '¥620,125.00')",
            "INSERT INTO hotel_budget_info (label, value) VALUES ('集团市场共享费（Co-op Fund）', '¥349,561.33')",
            "INSERT INTO hotel_budget_info (label, value) VALUES ('PMP', '¥60,000.00')",
            "INSERT INTO hotel_budget_info (label, value) VALUES ('总预算', '¥1,029,686.33')"
          ]

          // 批量插入USP信息
          const uspInfoInserts = [
            `INSERT INTO hotel_usp_info (category, items) VALUES ('rooms', '${JSON.stringify(['家庭房', '景观房', '独特风格房型', '亲子房'])}')`,
            `INSERT INTO hotel_usp_info (category, items) VALUES ('dining', '${JSON.stringify(['免费早餐', '餐厅拥有佳景', '国际美食'])}')`,
            `INSERT INTO hotel_usp_info (category, items) VALUES ('meeting', '${JSON.stringify(['1000平米无柱宴会厅', '40平米高清LED', '10种会议室组合'])}')`,
            `INSERT INTO hotel_usp_info (category, items) VALUES ('services', '${JSON.stringify(['室外泳池/儿童泳池', 'SPA', '运动中心、健身房'])}')`
          ]

          // 执行所有插入语句
          const allInserts = [...userInserts, ...basicInfoInserts, ...budgetInfoInserts, ...uspInfoInserts]
          for (const sql of allInserts) {
            this.db.exec(sql)
          }

          // 提交事务
          this.db.exec('COMMIT')

          // 保存到文件
          this.save()

          console.log(`✅ SQLite默认数据初始化完成 (${Date.now() - startTime}ms)`)
        } catch (error) {
          // 回滚事务
          this.db.exec('ROLLBACK')
          throw error
        }
      } else {
        console.log('✅ SQLite数据已存在，跳过初始化')
      }
    } catch (error) {
      console.error('❌ SQLite数据初始化失败:', error)
      throw error
    }
  }

  // 获取数据库信息
  getInfo(): { type: string; connection: string; description: string } {
    return {
      type: 'SQLite',
      connection: this.dbPath,
      description: 'SQLite文件数据库 - 适合开发和小型应用'
    }
  }
}
