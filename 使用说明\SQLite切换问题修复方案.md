# 🔧 SQLite切换问题修复方案

## 🎯 **问题分析**

从错误日志可以看出，切换到SQLite时出现以下问题：

1. **`Error: Please install sqlite3 package manually`** - Sequelize尝试加载sqlite3包
2. **SQLite适配器未初始化** - 切换逻辑有问题
3. **虚拟Sequelize实例创建失败** - 仍然使用SQLite方言

## 🛠️ **根本原因**

项目使用了**sql.js**作为SQLite实现，但是切换数据库时，代码仍然尝试创建真正的Sequelize SQLite实例，而Sequelize需要sqlite3包。

## ✅ **解决方案**

### 方案1: 安装sqlite3包（推荐）

```bash
# 使用npm安装
npm install sqlite3

# 或使用pnpm安装
pnpm add sqlite3
```

**优点**: 
- 彻底解决Sequelize依赖问题
- 支持真正的SQLite数据库
- 兼容性最好

**缺点**: 
- 需要编译原生模块
- 可能遇到网络问题

### 方案2: 修改切换逻辑（当前实现）

我已经修改了代码，避免创建真正的SQLite Sequelize实例：

```typescript
// 在database-config.ts中
if (config.type === 'sqlite') {
  // 使用MySQL方言避免sqlite3依赖问题
  return new Sequelize('mysql://dummy:dummy@localhost/dummy', {
    dialect: 'mysql',
    logging: false,
    pool: { max: 1, min: 1 }
  })
}

// 在database.ts中
if (type === 'sqlite') {
  // 初始化SQLite适配器
  await initSQLiteAdapter()
  // 创建虚拟Sequelize实例（使用MySQL方言）
  sequelize = new Sequelize('mysql://dummy:dummy@localhost/dummy', {
    dialect: 'mysql',
    logging: false,
    pool: { max: 1, min: 1 }
  })
}
```

### 方案3: 禁用SQLite切换（临时方案）

如果上述方案都不行，可以临时禁用SQLite切换功能：

```typescript
// 在前端API中添加检查
export const switchDatabase = (type: 'mysql' | 'sqlite') => {
  if (type === 'sqlite') {
    throw new Error('SQLite切换功能暂时不可用，请使用MySQL')
  }
  return request.post<DatabaseInfo>({ url: '/database/switch', data: { type } })
}
```

## 🚀 **推荐操作步骤**

### 步骤1: 尝试安装sqlite3

```bash
# 清理缓存
npm cache clean --force

# 重新安装sqlite3
npm install sqlite3 --save

# 如果失败，尝试使用淘宝镜像
npm install sqlite3 --registry=https://registry.npmmirror.com
```

### 步骤2: 如果安装失败，使用当前修复

当前代码已经修改为使用虚拟MySQL实例，应该可以避免sqlite3依赖问题。

### 步骤3: 重新启动应用

```bash
# 重新构建并启动
npm run electron
```

### 步骤4: 测试切换功能

1. 打开API演示页面
2. 点击"一键切换到SQLite"
3. 查看是否成功切换

## 🔍 **调试信息**

### 检查当前状态

1. **查看终端输出** - 是否还有sqlite3错误
2. **检查数据库信息** - 切换后是否显示SQLite
3. **测试API功能** - 用户管理是否正常

### 预期结果

切换成功后应该看到：

```
🔄 正在切换到 SQLITE 数据库...
🔗 使用SQLite数据库（sql.js）...
🔄 正在初始化SQLite数据库...
📦 加载sql.js WebAssembly模块...
✅ sql.js加载完成 (XXXms)
🆕 创建新的SQLite数据库
✅ SQLite数据库初始化完成 (总耗时: XXXms)
✅ 已成功切换到 SQLITE 数据库
```

## 🎯 **长期解决方案**

### 选项1: 完全移除Sequelize依赖

为SQLite创建独立的数据访问层，不依赖Sequelize：

```typescript
class SQLiteOnlyDAO {
  // 直接使用sql.js，不依赖Sequelize
  async getUsers() {
    return this.adapter.getUsers()
  }
  
  async createUser(user) {
    return this.adapter.createUser(user)
  }
}
```

### 选项2: 使用更轻量的ORM

考虑使用不需要原生依赖的ORM，如：
- **Prisma** - 支持多种数据库
- **TypeORM** - 更好的TypeScript支持
- **Drizzle** - 轻量级ORM

### 选项3: 统一使用MySQL

如果SQLite功能不是必需的，可以考虑统一使用MySQL：

```typescript
// 简化配置，只支持MySQL
const config = {
  type: 'mysql',
  mysql: {
    host: '127.0.0.1',
    port: 3306,
    username: 'root',
    password: 'zy123good',
    database: 'hiltonmarket'
  }
}
```

## 📋 **总结**

当前问题的核心是**sqlite3包缺失**导致Sequelize无法创建SQLite实例。

**最佳解决方案**是安装sqlite3包，但如果安装失败，当前的虚拟实例方案应该可以作为临时解决方案。

**请先尝试重新启动应用，看看当前修复是否有效！**
