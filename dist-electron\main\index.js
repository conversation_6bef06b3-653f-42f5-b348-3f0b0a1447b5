"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
const electron = require("electron");
const os = require("os");
const path = require("path");
const express = require("express");
const cors = require("cors");
const sequelize$1 = require("sequelize");
const fs = require("fs");
const initSqlJs = require("sql.js");
class SQLiteAdapter {
  constructor(dbPath) {
    __publicField(this, "SQL");
    __publicField(this, "db");
    __publicField(this, "dbPath");
    this.dbPath = dbPath || path.join(electron.app.getPath("userData"), "hiltonmarket.db");
  }
  // 初始化SQLite
  async init() {
    try {
      console.log("🔄 正在初始化SQLite数据库...");
      const startTime = Date.now();
      console.log("📦 加载sql.js WebAssembly模块...");
      this.SQL = await initSqlJs({
        // 可以指定wasm文件路径，这里使用默认
      });
      console.log(`✅ sql.js加载完成 (${Date.now() - startTime}ms)`);
      if (fs.existsSync(this.dbPath)) {
        console.log("📂 加载现有SQLite数据库文件");
        const filebuffer = fs.readFileSync(this.dbPath);
        this.db = new this.SQL.Database(filebuffer);
      } else {
        console.log("🆕 创建新的SQLite数据库");
        this.db = new this.SQL.Database();
      }
      this.createTables();
      console.log(`✅ SQLite数据库初始化完成 (总耗时: ${Date.now() - startTime}ms)`);
    } catch (error) {
      console.error("❌ SQLite初始化失败:", error);
      throw error;
    }
  }
  // 创建表结构
  async createTables() {
    const tables = [
      // 用户表
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        role TEXT NOT NULL DEFAULT 'user',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      // 酒店基本信息表
      `CREATE TABLE IF NOT EXISTS hotel_basic_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        value TEXT NOT NULL,
        note TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      // 酒店预算信息表
      `CREATE TABLE IF NOT EXISTS hotel_budget_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        value TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      // 酒店USP信息表
      `CREATE TABLE IF NOT EXISTS hotel_usp_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        items TEXT NOT NULL, -- JSON字符串
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];
    for (const sql of tables) {
      this.db.run(sql);
    }
  }
  // 执行查询
  query(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.getAsObject(params);
      return Array.isArray(result) ? result : [result];
    } catch (error) {
      console.error("SQLite查询错误:", error);
      throw error;
    }
  }
  // 执行查询并返回所有结果
  all(sql, params = []) {
    try {
      const stmt = this.db.prepare(sql);
      const results = [];
      while (stmt.step()) {
        results.push(stmt.getAsObject());
      }
      stmt.free();
      return results;
    } catch (error) {
      console.error("SQLite查询错误:", error);
      throw error;
    }
  }
  // 执行查询并返回第一个结果
  get(sql, params = []) {
    const results = this.all(sql, params);
    return results.length > 0 ? results[0] : null;
  }
  // 执行插入/更新/删除
  run(sql, params = []) {
    var _a, _b;
    try {
      const stmt = this.db.prepare(sql);
      stmt.run(params);
      const result = {
        lastID: ((_b = (_a = this.db.exec("SELECT last_insert_rowid() as id")[0]) == null ? void 0 : _a.values[0]) == null ? void 0 : _b[0]) || 0,
        changes: this.db.getRowsModified()
      };
      stmt.free();
      return result;
    } catch (error) {
      console.error("SQLite执行错误:", error);
      throw error;
    }
  }
  // 保存数据库到文件
  save() {
    try {
      const data = this.db.export();
      fs.writeFileSync(this.dbPath, data);
      console.log("💾 SQLite数据库已保存到文件");
    } catch (error) {
      console.error("❌ 保存SQLite数据库失败:", error);
      throw error;
    }
  }
  // 关闭数据库
  close() {
    try {
      if (this.db) {
        this.save();
        this.db.close();
        console.log("✅ SQLite数据库已关闭");
      }
    } catch (error) {
      console.error("❌ 关闭SQLite数据库失败:", error);
    }
  }
  // 初始化默认数据（优化版本）
  async initializeData() {
    var _a;
    try {
      console.log("🔍 检查SQLite数据初始化状态...");
      const startTime = Date.now();
      const userCount = ((_a = this.get("SELECT COUNT(*) as count FROM users")) == null ? void 0 : _a.count) || 0;
      if (userCount === 0) {
        console.log("📝 开始初始化SQLite默认数据...");
        this.db.exec("BEGIN TRANSACTION");
        try {
          const userInserts = [
            "INSERT INTO users (username, email, role) VALUES ('admin', '<EMAIL>', 'admin')",
            "INSERT INTO users (username, email, role) VALUES ('user', '<EMAIL>', 'user')"
          ];
          const basicInfoInserts = [
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('计划制定人', 'Alice', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店In Code', 'AOGCN', 'incode预处理数据')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店名称', '九寨沟康莱德酒店', '预填与incode匹配')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店所在区域', '西区', '预填与incode匹配')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('总经理', 'A', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('商务总监', 'B', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('市场总监', 'C', '用户手填')",
            "INSERT INTO hotel_basic_info (label, value, note) VALUES ('MECC 联系人', 'D', '用户手填')"
          ];
          const budgetInfoInserts = [
            "INSERT INTO hotel_budget_info (label, value) VALUES ('酒店本地活动预算', '¥620,125.00')",
            "INSERT INTO hotel_budget_info (label, value) VALUES ('集团市场共享费（Co-op Fund）', '¥349,561.33')",
            "INSERT INTO hotel_budget_info (label, value) VALUES ('PMP', '¥60,000.00')",
            "INSERT INTO hotel_budget_info (label, value) VALUES ('总预算', '¥1,029,686.33')"
          ];
          const uspInfoInserts = [
            `INSERT INTO hotel_usp_info (category, items) VALUES ('rooms', '${JSON.stringify(["家庭房", "景观房", "独特风格房型", "亲子房"])}')`,
            `INSERT INTO hotel_usp_info (category, items) VALUES ('dining', '${JSON.stringify(["免费早餐", "餐厅拥有佳景", "国际美食"])}')`,
            `INSERT INTO hotel_usp_info (category, items) VALUES ('meeting', '${JSON.stringify(["1000平米无柱宴会厅", "40平米高清LED", "10种会议室组合"])}')`,
            `INSERT INTO hotel_usp_info (category, items) VALUES ('services', '${JSON.stringify(["室外泳池/儿童泳池", "SPA", "运动中心、健身房"])}')`
          ];
          const allInserts = [...userInserts, ...basicInfoInserts, ...budgetInfoInserts, ...uspInfoInserts];
          for (const sql of allInserts) {
            this.db.exec(sql);
          }
          this.db.exec("COMMIT");
          this.save();
          console.log(`✅ SQLite默认数据初始化完成 (${Date.now() - startTime}ms)`);
        } catch (error) {
          this.db.exec("ROLLBACK");
          throw error;
        }
      } else {
        console.log("✅ SQLite数据已存在，跳过初始化");
      }
    } catch (error) {
      console.error("❌ SQLite数据初始化失败:", error);
      throw error;
    }
  }
  // 获取数据库信息
  getInfo() {
    return {
      type: "SQLite",
      connection: this.dbPath,
      description: "SQLite文件数据库 - 适合开发和小型应用"
    };
  }
}
const DEFAULT_CONFIG = {
  type: "sqlite",
  // 默认使用SQLite（轻量级，无需外部数据库）
  mysql: {
    host: "127.0.0.1",
    port: 3306,
    username: "root",
    password: "zy123good",
    database: "hiltonmarket"
  },
  sqlite: {
    storage: path.join(electron.app.getPath("userData"), "hiltonmarket.db")
  }
};
let currentConfig = { ...DEFAULT_CONFIG };
let sqliteAdapter = null;
const getDatabaseConfig = () => {
  return currentConfig;
};
const setDatabaseType = (type) => {
  currentConfig.type = type;
  console.log(`🔄 数据库类型切换为: ${type.toUpperCase()}`);
};
const updateMySQLConfig = (config) => {
  currentConfig.mysql = { ...currentConfig.mysql, ...config };
  console.log("🔧 MySQL配置已更新");
};
const updateSQLiteConfig = (config) => {
  currentConfig.sqlite = { ...currentConfig.sqlite, ...config };
  console.log("🔧 SQLite配置已更新");
};
const createSequelizeInstance = () => {
  const config = getDatabaseConfig();
  if (config.type === "mysql") {
    console.log("🔗 连接MySQL数据库...");
    return new sequelize$1.Sequelize({
      dialect: "mysql",
      host: config.mysql.host,
      port: config.mysql.port,
      username: config.mysql.username,
      password: config.mysql.password,
      database: config.mysql.database,
      logging: false,
      pool: {
        max: 3,
        min: 1,
        acquire: 1e4,
        idle: 5e3
      },
      dialectOptions: {
        connectTimeout: 5e3,
        acquireTimeout: 5e3,
        timeout: 5e3
      }
    });
  } else {
    console.log("🔗 使用SQLite数据库（sql.js）...");
    throw new Error("SQLite模式下不使用Sequelize，请使用SQLiteAdapter");
  }
};
const getSQLiteAdapter = () => {
  return sqliteAdapter;
};
const initSQLiteAdapter = async () => {
  const config = getDatabaseConfig();
  if (config.type === "sqlite") {
    sqliteAdapter = new SQLiteAdapter(config.sqlite.storage);
    await sqliteAdapter.init();
    await sqliteAdapter.initializeData();
  }
};
const closeSQLiteAdapter = () => {
  if (sqliteAdapter) {
    sqliteAdapter.close();
    sqliteAdapter = null;
  }
};
const getDatabaseInfo = () => {
  const config = getDatabaseConfig();
  if (config.type === "mysql") {
    return {
      type: "MySQL",
      connection: `${config.mysql.host}:${config.mysql.port}/${config.mysql.database}`,
      description: "MySQL数据库 - 适合生产环境"
    };
  } else {
    return {
      type: "SQLite",
      connection: config.sqlite.storage,
      description: "SQLite文件数据库 - 适合开发和小型应用"
    };
  }
};
const loadConfigFromEnv = () => {
  const dbType = process.env.DB_TYPE;
  if (dbType && ["mysql", "sqlite"].includes(dbType)) {
    setDatabaseType(dbType);
  }
  if (process.env.DB_HOST) {
    updateMySQLConfig({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT ? parseInt(process.env.DB_PORT) : 3306,
      username: process.env.DB_USERNAME || "root",
      password: process.env.DB_PASSWORD || "",
      database: process.env.DB_DATABASE || "hiltonmarket"
    });
  }
  if (process.env.SQLITE_STORAGE) {
    updateSQLiteConfig({
      storage: process.env.SQLITE_STORAGE
    });
  }
};
const validateConfig = async (sequelize2) => {
  try {
    await sequelize2.authenticate();
    const info = getDatabaseInfo();
    console.log(`✅ ${info.type}数据库连接成功`);
    console.log(`📍 连接地址: ${info.connection}`);
    return true;
  } catch (error) {
    const info = getDatabaseInfo();
    console.error(`❌ ${info.type}数据库连接失败:`, error);
    return false;
  }
};
class SQLiteDAO {
  get adapter() {
    const adapter = getSQLiteAdapter();
    if (!adapter) {
      throw new Error("SQLite适配器未初始化");
    }
    return adapter;
  }
  // 用户相关操作
  async getUsers(options = {}) {
    const { limit = 10, offset = 0, search = "" } = options;
    let sql = "SELECT * FROM users";
    let countSql = "SELECT COUNT(*) as count FROM users";
    const params = [];
    if (search) {
      const searchCondition = " WHERE username LIKE ? OR email LIKE ?";
      sql += searchCondition;
      countSql += searchCondition;
      params.push(`%${search}%`, `%${search}%`);
    }
    sql += " ORDER BY id ASC LIMIT ? OFFSET ?";
    params.push(limit, offset);
    const users = this.adapter.all(sql, params);
    const countResult = this.adapter.get(countSql, search ? [`%${search}%`, `%${search}%`] : []);
    return {
      users,
      total: (countResult == null ? void 0 : countResult.count) || 0
    };
  }
  async getUserById(id) {
    return this.adapter.get("SELECT * FROM users WHERE id = ?", [id]);
  }
  async createUser(userData) {
    const { username, email, role = "user" } = userData;
    const result = this.adapter.run(
      "INSERT INTO users (username, email, role) VALUES (?, ?, ?)",
      [username, email, role]
    );
    return this.getUserById(result.lastID);
  }
  async updateUser(id, userData) {
    const fields = [];
    const params = [];
    if (userData.username) {
      fields.push("username = ?");
      params.push(userData.username);
    }
    if (userData.email) {
      fields.push("email = ?");
      params.push(userData.email);
    }
    if (userData.role) {
      fields.push("role = ?");
      params.push(userData.role);
    }
    if (fields.length === 0) {
      return this.getUserById(id);
    }
    fields.push("updatedAt = CURRENT_TIMESTAMP");
    params.push(id);
    this.adapter.run(
      `UPDATE users SET ${fields.join(", ")} WHERE id = ?`,
      params
    );
    return this.getUserById(id);
  }
  async deleteUser(id) {
    const user = this.getUserById(id);
    this.adapter.run("DELETE FROM users WHERE id = ?", [id]);
    return user;
  }
  // 酒店基本信息操作
  async getHotelBasicInfo() {
    return this.adapter.all("SELECT * FROM hotel_basic_info ORDER BY id ASC");
  }
  async updateHotelBasicInfo(basicInfo) {
    this.adapter.run("DELETE FROM hotel_basic_info");
    for (const item of basicInfo) {
      this.adapter.run(
        "INSERT INTO hotel_basic_info (label, value, note) VALUES (?, ?, ?)",
        [item.label, item.value, item.note || null]
      );
    }
    return this.getHotelBasicInfo();
  }
  // 酒店预算信息操作
  async getHotelBudgetInfo() {
    return this.adapter.all("SELECT * FROM hotel_budget_info ORDER BY id ASC");
  }
  async updateHotelBudgetInfo(budgetInfo) {
    this.adapter.run("DELETE FROM hotel_budget_info");
    for (const item of budgetInfo) {
      this.adapter.run(
        "INSERT INTO hotel_budget_info (label, value) VALUES (?, ?)",
        [item.label, item.value]
      );
    }
    return this.getHotelBudgetInfo();
  }
  // 酒店USP信息操作
  async getHotelUspInfo() {
    const results = this.adapter.all("SELECT * FROM hotel_usp_info ORDER BY id ASC");
    return results.map((item) => ({
      ...item,
      items: JSON.parse(item.items)
    }));
  }
  async updateHotelUspInfo(uspData) {
    this.adapter.run("DELETE FROM hotel_usp_info");
    if (uspData.length > 0) {
      const usp = uspData[0];
      for (const [category, items] of Object.entries(usp)) {
        this.adapter.run(
          "INSERT INTO hotel_usp_info (category, items) VALUES (?, ?)",
          [category, JSON.stringify(items)]
        );
      }
    }
    return this.getHotelUspInfo();
  }
  // 获取完整酒店信息
  async getHotelInfo() {
    const basicInfo = await this.getHotelBasicInfo();
    const budgetInfo = await this.getHotelBudgetInfo();
    const uspData = await this.getHotelUspInfo();
    const uspFormatted = uspData.reduce((acc, item) => {
      acc[item.category] = item.items;
      return acc;
    }, {});
    return {
      basicInfo,
      budgetInfo,
      uspData: [uspFormatted]
    };
  }
  // 检查表是否存在
  async checkTablesExist() {
    const tables = this.adapter.all(`
      SELECT name FROM sqlite_master
      WHERE type = 'table'
      AND name IN ('users', 'hotel_basic_info', 'hotel_budget_info', 'hotel_usp_info')
    `);
    return tables.length === 4;
  }
  // 检查是否需要初始化数据
  async checkNeedsInitialization() {
    var _a;
    const userCount = ((_a = this.adapter.get("SELECT COUNT(*) as count FROM users")) == null ? void 0 : _a.count) || 0;
    return userCount === 0;
  }
}
const sqliteDAO = new SQLiteDAO();
let sequelize;
const initSequelize = async () => {
  loadConfigFromEnv();
  sequelize = createSequelizeInstance();
  const isValid = await validateConfig(sequelize);
  if (!isValid) {
    throw new Error("数据库连接验证失败");
  }
  return sequelize;
};
const getSequelize = () => {
  const config = getDatabaseConfig();
  if (config.type === "sqlite") {
    throw new Error("SQLite模式下不使用Sequelize，请使用SQLiteAdapter");
  }
  if (!sequelize) {
    throw new Error("数据库未初始化，请先调用 initSequelize()");
  }
  return sequelize;
};
const switchDatabase = async (type) => {
  console.log(`🔄 正在切换到 ${type.toUpperCase()} 数据库...`);
  if (sequelize) {
    await sequelize.close();
  }
  if (getDatabaseConfig().type === "sqlite") {
    closeSQLiteAdapter();
  }
  setDatabaseType(type);
  if (type === "sqlite") {
    await initSQLiteAdapter();
    sequelize = null;
  } else {
    sequelize = await initSequelize();
    defineModels();
  }
  console.log(`✅ 已成功切换到 ${type.toUpperCase()} 数据库`);
};
class User extends sequelize$1.Model {
}
class HotelBasicInfo extends sequelize$1.Model {
}
class HotelBudgetInfo extends sequelize$1.Model {
}
class HotelUspInfo extends sequelize$1.Model {
}
const defineModels = () => {
  const seq = getSequelize();
  User.init(
    {
      id: {
        type: sequelize$1.DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      username: {
        type: sequelize$1.DataTypes.STRING(50),
        allowNull: false,
        unique: true
      },
      email: {
        type: sequelize$1.DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true
        }
      },
      role: {
        type: sequelize$1.DataTypes.ENUM("admin", "user"),
        allowNull: false,
        defaultValue: "user"
      }
    },
    {
      sequelize: seq,
      tableName: "users",
      timestamps: true
    }
  );
  HotelBasicInfo.init(
    {
      id: {
        type: sequelize$1.DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      label: {
        type: sequelize$1.DataTypes.STRING(100),
        allowNull: false
      },
      value: {
        type: sequelize$1.DataTypes.TEXT,
        allowNull: false
      },
      note: {
        type: sequelize$1.DataTypes.STRING(200),
        allowNull: true
      }
    },
    {
      sequelize: seq,
      tableName: "hotel_basic_info",
      timestamps: true
    }
  );
  HotelBudgetInfo.init(
    {
      id: {
        type: sequelize$1.DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      label: {
        type: sequelize$1.DataTypes.STRING(100),
        allowNull: false
      },
      value: {
        type: sequelize$1.DataTypes.STRING(100),
        allowNull: false
      }
    },
    {
      sequelize: seq,
      tableName: "hotel_budget_info",
      timestamps: true
    }
  );
  HotelUspInfo.init(
    {
      id: {
        type: sequelize$1.DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
      },
      category: {
        type: sequelize$1.DataTypes.STRING(50),
        allowNull: false
      },
      items: {
        type: sequelize$1.DataTypes.JSON,
        allowNull: false
      }
    },
    {
      sequelize: seq,
      tableName: "hotel_usp_info",
      timestamps: true
    }
  );
};
const initDatabase = async () => {
  const startTime = Date.now();
  console.log("🚀 开始数据库初始化...");
  try {
    loadConfigFromEnv();
    const config = getDatabaseConfig();
    const dbInfo = getDatabaseInfo();
    console.log(`📊 当前使用: ${dbInfo.description}`);
    if (config.type === "mysql") {
      console.log("🔗 初始化MySQL数据库...");
      const mysqlStartTime = Date.now();
      await initSequelize();
      defineModels();
      console.log(`✅ MySQL连接建立 (${Date.now() - mysqlStartTime}ms)`);
      const [tableExists, needsInitData] = await Promise.all([
        checkTablesExist(),
        checkNeedsInitialization().catch(() => true)
        // 如果检查失败，假设需要初始化
      ]);
      if (!tableExists) {
        console.log("🔧 首次运行，创建数据库表...");
        const syncStartTime = Date.now();
        await getSequelize().sync({ force: false });
        console.log(`✅ 数据库表创建完成 (${Date.now() - syncStartTime}ms)`);
        const dataStartTime = Date.now();
        await initializeData();
        console.log(`✅ 初始数据创建完成 (${Date.now() - dataStartTime}ms)`);
      } else {
        console.log("✅ 数据库表已存在，跳过同步");
        if (needsInitData) {
          const dataStartTime = Date.now();
          await initializeData();
          console.log(`✅ 初始数据补充完成 (${Date.now() - dataStartTime}ms)`);
        } else {
          console.log("✅ 数据已存在，跳过初始化");
        }
      }
    } else {
      console.log("🔗 初始化SQLite数据库...");
      await initSQLiteAdapter();
    }
    console.log(`🎉 数据库初始化完成！总耗时: ${Date.now() - startTime}ms`);
  } catch (error) {
    console.error(`❌ 数据库初始化失败 (耗时: ${Date.now() - startTime}ms):`, error);
    throw error;
  }
};
const checkTablesExist = async () => {
  try {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const seq = getSequelize();
      const [results] = await seq.query(`
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = 'hiltonmarket'
        AND table_name IN ('users', 'hotel_basic_info', 'hotel_budget_info', 'hotel_usp_info')
      `);
      return results[0].count === 4;
    } else {
      return await sqliteDAO.checkTablesExist();
    }
  } catch (error) {
    return false;
  }
};
const checkNeedsInitialization = async () => {
  try {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const userCount = await User.count();
      return userCount === 0;
    } else {
      return await sqliteDAO.checkNeedsInitialization();
    }
  } catch (error) {
    return true;
  }
};
const initializeData = async () => {
  try {
    await sequelize.transaction(async (t) => {
      await Promise.all([
        // 创建默认用户
        User.bulkCreate([
          { username: "admin", email: "<EMAIL>", role: "admin" },
          { username: "user", email: "<EMAIL>", role: "user" }
        ], { transaction: t, ignoreDuplicates: true }),
        // 创建默认酒店基本信息
        HotelBasicInfo.bulkCreate([
          { label: "计划制定人", value: "Alice", note: "用户手填" },
          { label: "酒店In Code", value: "AOGCN", note: "incode预处理数据" },
          { label: "酒店名称", value: "九寨沟康莱德酒店", note: "预填与incode匹配" },
          { label: "酒店所在区域", value: "西区", note: "预填与incode匹配" },
          { label: "总经理", value: "A", note: "用户手填" },
          { label: "商务总监", value: "B", note: "用户手填" },
          { label: "市场总监", value: "C", note: "用户手填" },
          { label: "MECC 联系人", value: "D", note: "用户手填" }
        ], { transaction: t, ignoreDuplicates: true }),
        // 创建默认预算信息
        HotelBudgetInfo.bulkCreate([
          { label: "酒店本地活动预算", value: "¥620,125.00" },
          { label: "集团市场共享费（Co-op Fund）", value: "¥349,561.33" },
          { label: "PMP", value: "¥60,000.00" },
          { label: "总预算", value: "¥1,029,686.33" }
        ], { transaction: t, ignoreDuplicates: true }),
        // 创建默认USP信息
        HotelUspInfo.bulkCreate([
          { category: "rooms", items: ["家庭房", "景观房", "独特风格房型", "亲子房"] },
          { category: "dining", items: ["免费早餐", "餐厅拥有佳景", "国际美食"] },
          { category: "meeting", items: ["1000平米无柱宴会厅", "40平米高清LED", "10种会议室组合"] },
          { category: "services", items: ["室外泳池/儿童泳池", "SPA", "运动中心、健身房"] }
        ], { transaction: t, ignoreDuplicates: true })
      ]);
    });
    console.log("📝 默认数据初始化完成");
  } catch (error) {
    console.error("❌ 初始化数据失败:", error);
    throw error;
  }
};
const closeDatabase = async () => {
  try {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      if (sequelize) {
        await sequelize.close();
        console.log("✅ MySQL数据库连接已关闭");
      }
    } else {
      closeSQLiteAdapter();
      console.log("✅ SQLite数据库已关闭");
    }
  } catch (error) {
    console.error("❌ 关闭数据库连接失败:", error);
  }
};
let server = null;
let serverPort = 8e3;
const dbOperations = {
  // 用户操作
  async getUsers(options) {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const { page, limit, search } = options;
      const offset = (page - 1) * limit;
      const whereCondition = {};
      if (search) {
        whereCondition[sequelize$1.Op.or] = [
          { username: { [sequelize$1.Op.like]: `%${search}%` } },
          { email: { [sequelize$1.Op.like]: `%${search}%` } }
        ];
      }
      const { count, rows } = await User.findAndCountAll({
        where: whereCondition,
        limit,
        offset,
        order: [["id", "ASC"]],
        attributes: ["id", "username", "email", "role", "createdAt", "updatedAt"]
      });
      return { users: rows.map((user) => user.toJSON()), total: count };
    } else {
      return await sqliteDAO.getUsers({
        limit: options.limit,
        offset: (options.page - 1) * options.limit,
        search: options.search
      });
    }
  },
  async getUserById(id) {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const user = await User.findByPk(id, {
        attributes: ["id", "username", "email", "role", "createdAt", "updatedAt"]
      });
      return user ? user.toJSON() : null;
    } else {
      return await sqliteDAO.getUserById(id);
    }
  },
  async createUser(userData) {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const newUser = await User.create(userData);
      return newUser.toJSON();
    } else {
      return await sqliteDAO.createUser(userData);
    }
  },
  async updateUser(id, userData) {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const user = await User.findByPk(id);
      if (!user)
        return null;
      await user.update(userData);
      return user.toJSON();
    } else {
      return await sqliteDAO.updateUser(id, userData);
    }
  },
  async deleteUser(id) {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const user = await User.findByPk(id);
      if (!user)
        return null;
      const userData = user.toJSON();
      await user.destroy();
      return userData;
    } else {
      return await sqliteDAO.deleteUser(id);
    }
  },
  // 酒店信息操作
  async getHotelInfo() {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const basicInfo = await HotelBasicInfo.findAll({
        attributes: ["label", "value", "note"],
        order: [["id", "ASC"]]
      });
      const budgetInfo = await HotelBudgetInfo.findAll({
        attributes: ["label", "value"],
        order: [["id", "ASC"]]
      });
      const uspData = await HotelUspInfo.findAll({
        attributes: ["category", "items"],
        order: [["id", "ASC"]]
      });
      const uspFormatted = uspData.reduce((acc, item) => {
        acc[item.category] = item.items;
        return acc;
      }, {});
      return {
        basicInfo: basicInfo.map((item) => item.toJSON()),
        budgetInfo: budgetInfo.map((item) => item.toJSON()),
        uspData: [uspFormatted]
      };
    } else {
      return await sqliteDAO.getHotelInfo();
    }
  },
  async getHotelBasicInfo() {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      const basicInfo = await HotelBasicInfo.findAll({
        attributes: ["label", "value", "note"],
        order: [["id", "ASC"]]
      });
      return basicInfo.map((item) => item.toJSON());
    } else {
      return await sqliteDAO.getHotelBasicInfo();
    }
  },
  async updateHotelBasicInfo(basicInfo) {
    const config = getDatabaseConfig();
    if (config.type === "mysql") {
      await HotelBasicInfo.destroy({ where: {} });
      const newBasicInfo = await HotelBasicInfo.bulkCreate(basicInfo);
      return newBasicInfo.map((item) => item.toJSON());
    } else {
      return await sqliteDAO.updateHotelBasicInfo(basicInfo);
    }
  }
};
const findAvailablePort = (startPort) => {
  return new Promise((resolve) => {
    const net = require("net");
    const server2 = net.createServer();
    server2.listen(startPort, () => {
      var _a;
      const port = (_a = server2.address()) == null ? void 0 : _a.port;
      server2.close(() => {
        resolve(port || startPort);
      });
    });
    server2.on("error", () => {
      findAvailablePort(startPort + 1).then(resolve);
    });
  });
};
const startApiServer = async () => {
  try {
    await initDatabase();
    serverPort = await findAvailablePort(8e3);
    const app = express();
    app.use(cors());
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    if (electron.app.isPackaged) {
      const uploadsPath = path.join(process.resourcesPath, "uploads");
      app.use("/uploads", express.static(uploadsPath));
    } else {
      app.use("/uploads", express.static(path.join(__dirname, "../../server/uploads")));
    }
    app.get("/health", (req, res) => {
      const dbInfo = getDatabaseInfo();
      res.json({
        code: 0,
        message: "success",
        data: {
          status: "OK",
          message: "Electron API Server is running",
          timestamp: (/* @__PURE__ */ new Date()).toISOString(),
          port: serverPort,
          database: {
            type: dbInfo.type,
            connection: dbInfo.connection,
            description: dbInfo.description
          }
        }
      });
    });
    app.get("/database/info", (req, res) => {
      try {
        const dbInfo = getDatabaseInfo();
        res.json({
          code: 0,
          message: "success",
          data: dbInfo
        });
      } catch (error) {
        console.error("获取数据库信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "获取数据库信息失败"
        });
      }
    });
    app.post("/database/switch", async (req, res) => {
      try {
        const { type } = req.body;
        if (!type || !["mysql", "sqlite"].includes(type)) {
          return res.status(400).json({
            code: 400,
            message: "无效的数据库类型，支持: mysql, sqlite"
          });
        }
        await switchDatabase(type);
        const dbInfo = getDatabaseInfo();
        res.json({
          code: 0,
          message: `成功切换到 ${type.toUpperCase()} 数据库`,
          data: dbInfo
        });
      } catch (error) {
        console.error("切换数据库失败:", error);
        res.status(500).json({
          code: 500,
          message: "切换数据库失败: " + error.message
        });
      }
    });
    app.get("/mock/analysis/total", (req, res) => {
      res.json({
        code: 0,
        data: {
          users: 102400,
          messages: 81212,
          moneys: 9280,
          shoppings: 13600
        }
      });
    });
    app.get("/mock/analysis/userAccessSource", (req, res) => {
      res.json({
        code: 0,
        data: [
          { value: 1e3, name: "analysis.directAccess" },
          { value: 310, name: "analysis.mailMarketing" },
          { value: 234, name: "analysis.allianceAdvertising" },
          { value: 135, name: "analysis.videoAdvertising" },
          { value: 1548, name: "analysis.searchEngines" }
        ]
      });
    });
    app.get("/mock/analysis/weeklyUserActivity", (req, res) => {
      res.json({
        code: 0,
        data: [
          { value: 13253, name: "analysis.monday" },
          { value: 34235, name: "analysis.tuesday" },
          { value: 26321, name: "analysis.wednesday" },
          { value: 12340, name: "analysis.thursday" },
          { value: 24643, name: "analysis.friday" },
          { value: 1322, name: "analysis.saturday" },
          { value: 1324, name: "analysis.sunday" }
        ]
      });
    });
    app.get("/mock/analysis/monthlySales", (req, res) => {
      res.json({
        code: 0,
        data: [
          { estimate: 100, actual: 120, name: "analysis.january" },
          { estimate: 120, actual: 82, name: "analysis.february" },
          { estimate: 161, actual: 91, name: "analysis.march" },
          { estimate: 134, actual: 154, name: "analysis.april" },
          { estimate: 105, actual: 162, name: "analysis.may" },
          { estimate: 160, actual: 140, name: "analysis.june" },
          { estimate: 165, actual: 145, name: "analysis.july" },
          { estimate: 114, actual: 250, name: "analysis.august" },
          { estimate: 163, actual: 134, name: "analysis.september" },
          { estimate: 185, actual: 56, name: "analysis.october" },
          { estimate: 118, actual: 99, name: "analysis.november" },
          { estimate: 123, actual: 123, name: "analysis.december" }
        ]
      });
    });
    app.get("/mock/workplace/total", (req, res) => {
      res.json({
        code: 0,
        data: {
          project: 40,
          access: 2340,
          todo: 10
        }
      });
    });
    app.get("/mock/workplace/project", (req, res) => {
      res.json({
        code: 0,
        data: [
          {
            name: "Github",
            icon: "akar-icons:github-fill",
            message: "workplace.introduction",
            personal: "Archer",
            time: /* @__PURE__ */ new Date()
          },
          {
            name: "Vue",
            icon: "logos:vue",
            message: "workplace.introduction",
            personal: "Archer",
            time: /* @__PURE__ */ new Date()
          },
          {
            name: "Angular",
            icon: "logos:angular-icon",
            message: "workplace.introduction",
            personal: "Archer",
            time: /* @__PURE__ */ new Date()
          },
          {
            name: "React",
            icon: "logos:react",
            message: "workplace.introduction",
            personal: "Archer",
            time: /* @__PURE__ */ new Date()
          },
          {
            name: "Webpack",
            icon: "logos:webpack",
            message: "workplace.introduction",
            personal: "Archer",
            time: /* @__PURE__ */ new Date()
          },
          {
            name: "Vite",
            icon: "vscode-icons:file-type-vite",
            message: "workplace.introduction",
            personal: "Archer",
            time: /* @__PURE__ */ new Date()
          }
        ]
      });
    });
    app.get("/mock/workplace/dynamic", (req, res) => {
      res.json({
        code: 0,
        data: [
          {
            keys: ["workplace.push", "Github"],
            time: /* @__PURE__ */ new Date()
          },
          {
            keys: ["workplace.push", "Github"],
            time: /* @__PURE__ */ new Date()
          },
          {
            keys: ["workplace.push", "Github"],
            time: /* @__PURE__ */ new Date()
          },
          {
            keys: ["workplace.push", "Github"],
            time: /* @__PURE__ */ new Date()
          },
          {
            keys: ["workplace.push", "Github"],
            time: /* @__PURE__ */ new Date()
          },
          {
            keys: ["workplace.push", "Github"],
            time: /* @__PURE__ */ new Date()
          }
        ]
      });
    });
    app.get("/mock/workplace/team", (req, res) => {
      res.json({
        code: 0,
        data: [
          {
            name: "Github",
            icon: "akar-icons:github-fill"
          },
          {
            name: "Vue",
            icon: "logos:vue"
          },
          {
            name: "Angular",
            icon: "logos:angular-icon"
          },
          {
            name: "React",
            icon: "logos:react"
          },
          {
            name: "Webpack",
            icon: "logos:webpack"
          },
          {
            name: "Vite",
            icon: "vscode-icons:file-type-vite"
          }
        ]
      });
    });
    app.get("/mock/workplace/radar", (req, res) => {
      res.json({
        code: 0,
        data: [
          { name: "workplace.quote", max: 65, personal: 42, team: 50 },
          { name: "workplace.contribution", max: 160, personal: 30, team: 140 },
          { name: "workplace.hot", max: 300, personal: 20, team: 28 },
          { name: "workplace.yield", max: 130, personal: 35, team: 35 },
          { name: "workplace.follow", max: 100, personal: 80, team: 90 }
        ]
      });
    });
    app.get("/hotel/info", async (req, res) => {
      try {
        const data = await dbOperations.getHotelInfo();
        res.json({
          code: 0,
          message: "success",
          data
        });
      } catch (error) {
        console.error("获取酒店信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "获取酒店信息失败"
        });
      }
    });
    app.get("/hotel/basic", async (req, res) => {
      try {
        const data = await dbOperations.getHotelBasicInfo();
        res.json({
          code: 0,
          message: "success",
          data
        });
      } catch (error) {
        console.error("获取酒店基本信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "获取酒店基本信息失败"
        });
      }
    });
    app.get("/hotel/budget", async (req, res) => {
      try {
        const budgetInfo = await HotelBudgetInfo.findAll({
          attributes: ["label", "value"],
          order: [["id", "ASC"]]
        });
        res.json({
          code: 0,
          message: "success",
          data: budgetInfo.map((item) => item.toJSON())
        });
      } catch (error) {
        console.error("获取酒店预算信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "获取酒店预算信息失败"
        });
      }
    });
    app.get("/hotel/usp", async (req, res) => {
      try {
        const uspData = await HotelUspInfo.findAll({
          attributes: ["category", "items"],
          order: [["id", "ASC"]]
        });
        const uspFormatted = uspData.reduce((acc, item) => {
          acc[item.category] = item.items;
          return acc;
        }, {});
        res.json({
          code: 0,
          message: "success",
          data: [uspFormatted]
        });
      } catch (error) {
        console.error("获取酒店USP信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "获取酒店USP信息失败"
        });
      }
    });
    app.put("/hotel/basic", async (req, res) => {
      try {
        const { basicInfo } = req.body;
        if (!basicInfo || !Array.isArray(basicInfo)) {
          return res.status(400).json({
            code: 400,
            message: "无效的数据格式"
          });
        }
        const data = await dbOperations.updateHotelBasicInfo(basicInfo);
        res.json({
          code: 0,
          message: "酒店基本信息更新成功",
          data
        });
      } catch (error) {
        console.error("更新酒店基本信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "更新酒店基本信息失败"
        });
      }
    });
    app.put("/hotel/budget", async (req, res) => {
      try {
        const { budgetInfo } = req.body;
        if (!budgetInfo || !Array.isArray(budgetInfo)) {
          return res.status(400).json({
            code: 400,
            message: "无效的数据格式"
          });
        }
        const config = getDatabaseConfig();
        if (config.type === "mysql") {
          await HotelBudgetInfo.destroy({ where: {} });
          const newBudgetInfo = await HotelBudgetInfo.bulkCreate(budgetInfo);
          res.json({
            code: 0,
            message: "酒店预算信息更新成功",
            data: newBudgetInfo.map((item) => item.toJSON())
          });
        } else {
          await sqliteDAO.updateHotelBudgetInfo(budgetInfo);
          const data = await sqliteDAO.getHotelBudgetInfo();
          res.json({
            code: 0,
            message: "酒店预算信息更新成功",
            data
          });
        }
      } catch (error) {
        console.error("更新酒店预算信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "更新酒店预算信息失败"
        });
      }
    });
    app.put("/hotel/usp", async (req, res) => {
      try {
        const { uspData } = req.body;
        if (!uspData || !Array.isArray(uspData)) {
          return res.status(400).json({
            code: 400,
            message: "无效的数据格式"
          });
        }
        const config = getDatabaseConfig();
        if (config.type === "mysql") {
          await HotelUspInfo.destroy({ where: {} });
          const uspRecords = [];
          if (uspData.length > 0) {
            const usp = uspData[0];
            for (const [category, items] of Object.entries(usp)) {
              uspRecords.push({
                category,
                items: JSON.stringify(items)
              });
            }
          }
          const newUspInfo = await HotelUspInfo.bulkCreate(uspRecords);
          res.json({
            code: 0,
            message: "酒店USP信息更新成功",
            data: newUspInfo.map((item) => item.toJSON())
          });
        } else {
          await sqliteDAO.updateHotelUspInfo(uspData);
          const data = await sqliteDAO.getHotelUspInfo();
          res.json({
            code: 0,
            message: "酒店USP信息更新成功",
            data
          });
        }
      } catch (error) {
        console.error("更新酒店USP信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "更新酒店USP信息失败"
        });
      }
    });
    app.put("/hotel/info", async (req, res) => {
      try {
        const { basicInfo, budgetInfo, uspData } = req.body;
        if (!basicInfo || !budgetInfo || !uspData) {
          return res.status(400).json({
            code: 400,
            message: "缺少必要的数据字段"
          });
        }
        if (basicInfo && Array.isArray(basicInfo)) {
          await dbOperations.updateHotelBasicInfo(basicInfo);
        }
        if (budgetInfo && Array.isArray(budgetInfo)) {
          const config = getDatabaseConfig();
          if (config.type === "mysql") {
            await HotelBudgetInfo.destroy({ where: {} });
            await HotelBudgetInfo.bulkCreate(budgetInfo);
          } else {
            await sqliteDAO.updateHotelBudgetInfo(budgetInfo);
          }
        }
        if (uspData && Array.isArray(uspData)) {
          const config = getDatabaseConfig();
          if (config.type === "mysql") {
            await HotelUspInfo.destroy({ where: {} });
            const uspRecords = [];
            if (uspData.length > 0) {
              const usp = uspData[0];
              for (const [category, items] of Object.entries(usp)) {
                uspRecords.push({
                  category,
                  items: JSON.stringify(items)
                });
              }
            }
            await HotelUspInfo.bulkCreate(uspRecords);
          } else {
            await sqliteDAO.updateHotelUspInfo(uspData);
          }
        }
        const updatedData = await dbOperations.getHotelInfo();
        res.json({
          code: 0,
          message: "酒店信息更新成功",
          data: updatedData
        });
      } catch (error) {
        console.error("更新酒店信息失败:", error);
        res.status(500).json({
          code: 500,
          message: "更新酒店信息失败"
        });
      }
    });
    app.get("/users", async (req, res) => {
      try {
        const { page = 1, limit = 10, search = "" } = req.query;
        const pageNum = Number(page);
        const limitNum = Number(limit);
        const result = await dbOperations.getUsers({
          page: pageNum,
          limit: limitNum,
          search
        });
        res.json({
          code: 0,
          message: "success",
          data: {
            users: result.users,
            total: result.total,
            page: pageNum,
            limit: limitNum
          }
        });
      } catch (error) {
        console.error("获取用户列表失败:", error);
        res.status(500).json({
          code: 500,
          message: "获取用户列表失败"
        });
      }
    });
    app.get("/users/:id", async (req, res) => {
      try {
        const { id } = req.params;
        const user = await User.findByPk(id, {
          attributes: ["id", "username", "email", "role", "createdAt", "updatedAt"]
        });
        if (user) {
          res.json({
            code: 0,
            message: "success",
            data: user.toJSON()
          });
        } else {
          res.status(404).json({
            code: 404,
            message: "用户不存在"
          });
        }
      } catch (error) {
        console.error("获取用户失败:", error);
        res.status(500).json({
          code: 500,
          message: "获取用户失败"
        });
      }
    });
    app.post("/users", async (req, res) => {
      try {
        const { username, email, role = "user" } = req.body;
        if (!username || !email) {
          return res.status(400).json({
            code: 400,
            message: "用户名和邮箱是必填项"
          });
        }
        const existingUser = await User.findOne({
          where: {
            [sequelize$1.Op.or]: [
              { username },
              { email }
            ]
          }
        });
        if (existingUser) {
          return res.status(400).json({
            code: 400,
            message: "用户名或邮箱已存在"
          });
        }
        const newUser = await User.create({
          username,
          email,
          role
        });
        res.status(201).json({
          code: 0,
          message: "用户创建成功",
          data: newUser.toJSON()
        });
      } catch (error) {
        console.error("创建用户失败:", error);
        res.status(500).json({
          code: 500,
          message: "创建用户失败"
        });
      }
    });
    app.put("/users/:id", async (req, res) => {
      try {
        const { id } = req.params;
        const { username, email, role } = req.body;
        const user = await User.findByPk(id);
        if (!user) {
          return res.status(404).json({
            code: 404,
            message: "用户不存在"
          });
        }
        if (username || email) {
          const whereCondition = {
            id: { [sequelize$1.Op.ne]: id }
          };
          if (username && email) {
            whereCondition[sequelize$1.Op.or] = [
              { username },
              { email }
            ];
          } else if (username) {
            whereCondition.username = username;
          } else if (email) {
            whereCondition.email = email;
          }
          const existingUser = await User.findOne({ where: whereCondition });
          if (existingUser) {
            return res.status(400).json({
              code: 400,
              message: "用户名或邮箱已被其他用户使用"
            });
          }
        }
        const updateData = {};
        if (username)
          updateData.username = username;
        if (email)
          updateData.email = email;
        if (role)
          updateData.role = role;
        await user.update(updateData);
        res.json({
          code: 0,
          message: "用户更新成功",
          data: user.toJSON()
        });
      } catch (error) {
        console.error("更新用户失败:", error);
        res.status(500).json({
          code: 500,
          message: "更新用户失败"
        });
      }
    });
    app.delete("/users/:id", async (req, res) => {
      try {
        const { id } = req.params;
        const user = await User.findByPk(id);
        if (!user) {
          return res.status(404).json({
            code: 404,
            message: "用户不存在"
          });
        }
        const userData = user.toJSON();
        await user.destroy();
        res.json({
          code: 0,
          message: "用户删除成功",
          data: userData
        });
      } catch (error) {
        console.error("删除用户失败:", error);
        res.status(500).json({
          code: 500,
          message: "删除用户失败"
        });
      }
    });
    app.use((err, req, res, next) => {
      console.error("API Error:", err.stack);
      res.status(500).json({
        code: 500,
        message: "服务器内部错误"
      });
    });
    app.use("*", (req, res) => {
      res.status(404).json({
        code: 404,
        message: "接口不存在"
      });
    });
    return new Promise((resolve, reject) => {
      server = app.listen(serverPort, "127.0.0.1", () => {
        console.log(`🚀 Electron API Server started on http://localhost:${serverPort}`);
        resolve(serverPort);
      });
      server.on("error", (err) => {
        console.error("Server start error:", err);
        reject(err);
      });
    });
  } catch (error) {
    console.error("Failed to start API server:", error);
    throw error;
  }
};
const stopApiServer = () => {
  return new Promise(async (resolve) => {
    try {
      await closeDatabase();
      if (server) {
        server.close(() => {
          console.log("🛑 API Server stopped");
          server = null;
          resolve();
        });
      } else {
        resolve();
      }
    } catch (error) {
      console.error("停止API服务器时出错:", error);
      resolve();
    }
  });
};
const getServerPort = () => {
  return serverPort;
};
process.env.DIST_ELECTRON = path.join(__dirname, "..");
process.env.DIST = path.join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = electron.app.isPackaged ? process.env.DIST : path.join(process.env.DIST_ELECTRON, "../public");
if (os.release().startsWith("6.1"))
  electron.app.disableHardwareAcceleration();
if (process.platform === "win32")
  electron.app.setAppUserModelId(electron.app.getName());
if (!electron.app.requestSingleInstanceLock()) {
  electron.app.quit();
  process.exit(0);
}
let win = null;
const preload = path.join(__dirname, "../preload/index.js");
const url = process.env.VITE_DEV_SERVER_URL;
const indexHtml = path.join(process.env.DIST, "index.html");
async function createWindow() {
  try {
    const port = await startApiServer();
    console.log(`✅ API Server started on port ${port}`);
  } catch (error) {
    console.error("❌ Failed to start API server:", error);
  }
  win = new electron.BrowserWindow({
    title: "菜单管理工具",
    icon: path.join(process.env.PUBLIC, "favicon.ico"),
    webPreferences: {
      preload,
      // Warning: Enable nodeIntegration and disable contextIsolation is not secure in production
      // Consider using contextBridge.exposeInMainWorld
      // Read more on https://www.electronjs.org/docs/latest/tutorial/context-isolation
      nodeIntegration: true,
      contextIsolation: false
    },
    frame: true
  });
  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(url);
  } else {
    win.loadFile(indexHtml);
  }
  win.maximize();
  win.webContents.on("did-finish-load", () => {
    win == null ? void 0 : win.webContents.send("main-process-message", (/* @__PURE__ */ new Date()).toLocaleString());
    win == null ? void 0 : win.webContents.send("api-server-port", getServerPort());
  });
  win.webContents.setWindowOpenHandler(({ url: url2 }) => {
    if (url2.startsWith("https:"))
      electron.shell.openExternal(url2);
    return { action: "deny" };
  });
}
electron.app.whenReady().then(createWindow);
electron.app.on("window-all-closed", async () => {
  try {
    await stopApiServer();
    console.log("✅ API Server stopped");
  } catch (error) {
    console.error("❌ Failed to stop API server:", error);
  }
  win = null;
  if (process.platform !== "darwin")
    electron.app.quit();
});
electron.app.on("second-instance", () => {
  if (win) {
    if (win.isMinimized())
      win.restore();
    win.focus();
  }
});
electron.app.on("activate", () => {
  const allWindows = electron.BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createWindow();
  }
});
electron.ipcMain.handle("open-win", (event, arg) => {
  const childWindow = new electron.BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: false
    }
  });
  if (electron.app.isPackaged) {
    childWindow.loadFile(indexHtml, { hash: arg });
  } else {
    childWindow.loadURL(`${url}#${arg}`);
  }
});
electron.ipcMain.on("window-min", function() {
  if (win) {
    win.minimize();
  }
});
electron.ipcMain.on("window-max", function() {
  if (win) {
    if (win.isMaximized()) {
      win.restore();
    } else {
      win.maximize();
    }
  }
});
electron.ipcMain.on("window-close", function() {
  if (win) {
    win.close();
  }
});
electron.ipcMain.handle("get-api-port", () => {
  return getServerPort();
});
electron.ipcMain.handle("get-api-status", () => {
  return {
    port: getServerPort(),
    url: `http://localhost:${getServerPort()}`,
    status: "running"
  };
});
