import request from '@/axios'

// 酒店信息相关API

// 获取酒店完整信息
export const getHotelInfo = () => {
  return request.get({ url: '/hotel/info' })
}

// 获取酒店基本信息
export const getHotelBasicInfo = () => {
  return request.get({ url: '/hotel/basic' })
}

// 获取酒店预算信息
export const getHotelBudgetInfo = () => {
  return request.get({ url: '/hotel/budget' })
}

// 获取酒店USP信息
export const getHotelUspInfo = () => {
  return request.get({ url: '/hotel/usp' })
}

// 更新酒店基本信息
export const updateHotelBasicInfo = (data: any) => {
  return request.put({ url: '/hotel/basic', data })
}

// 更新酒店预算信息
export const updateHotelBudgetInfo = (data: any) => {
  return request.put({ url: '/hotel/budget', data })
}

// 更新酒店USP信息
export const updateHotelUspInfo = (data: any) => {
  return request.put({ url: '/hotel/usp', data })
}

// 更新所有酒店信息
export const updateHotelInfo = (data: any) => {
  return request.put({ url: '/hotel/info', data })
}

// 用户相关API类型定义
export interface User {
  id: number
  username: string
  email: string
  role: string
}

export interface UserListParams {
  page?: number
  limit?: number
  search?: string
}

export interface UserListResponse {
  users: User[]
  total: number
  page: number
  limit: number
}

// 获取用户列表
export const getUserList = (params?: UserListParams) => {
  return request.get<UserListResponse>({ url: '/users', params })
}

// 获取单个用户
export const getUser = (id: number) => {
  return request.get<User>({ url: `/users/${id}` })
}

// 创建用户
export const createUser = (data: Omit<User, 'id'>) => {
  return request.post<User>({ url: '/users', data })
}

// 更新用户
export const updateUser = (id: number, data: Partial<Omit<User, 'id'>>) => {
  return request.put<User>({ url: `/users/${id}`, data })
}

// 删除用户
export const deleteUser = (id: number) => {
  return request.delete<User>({ url: `/users/${id}` })
}

// 数据库管理相关API
export interface DatabaseInfo {
  type: string
  connection: string
  description: string
}

// 获取数据库信息
export const getDatabaseInfo = () => {
  return request.get<DatabaseInfo>({ url: '/database/info' })
}

// 切换数据库
export const switchDatabase = (type: 'mysql' | 'sqlite') => {
  return request.post<DatabaseInfo>({ url: '/database/switch', data: { type } })
}

// 健康检查（包含数据库信息）
export const healthCheck = () => {
  return request.get({ url: '/health' })
}
