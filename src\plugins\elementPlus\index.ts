import type { App } from 'vue'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 需要全局引入一些组件，如ElScrollbar，不然一些下拉项样式有问题
import {
  ElLoading,
  ElScrollbar,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElCard,
  ElRow,
  ElCol,
  ElTag,
  ElMessage,
  ElIcon
} from 'element-plus'

const plugins = [ElLoading]

const components = [
  ElScrollbar,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElCard,
  ElRow,
  ElCol,
  ElTag,
  ElIcon
]

export const setupElementPlus = (app: App<Element>) => {
  // 为了确保所有组件都可用，使用全局安装
  if (import.meta.env.VITE_USE_ALL_ELEMENT_PLUS_STYLE === 'true') {
    app.use(ElementPlus)
    import('element-plus/dist/index.css')

    // 注册所有图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }
    return
  }

  // 如果不使用全局样式，则单独注册组件
  plugins.forEach((plugin) => {
    app.use(plugin)
  })

  components.forEach((component) => {
    app.component(component.name!, component)
  })

  // 注册所有图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

  // 手动注册全局消息组件
  app.config.globalProperties.$message = ElMessage
}
