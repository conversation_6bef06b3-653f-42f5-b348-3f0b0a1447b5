# Electron 项目构建说明

## 🎯 项目概述

本项目已成功集成了 Node.js Express API 服务器到 Electron 主进程中，实现了真正的一体化桌面应用。

## 🚀 构建和运行

### 开发模式

```bash
# 启动开发模式（包含前端热重载和Electron）
npm run electron

# 或者分别启动
npm run dev        # 启动前端开发服务器
npm run electron   # 启动Electron应用
```

### 生产构建

```bash
# 构建Electron应用为exe文件
npm run build:electron

# 构建后的文件位置
# Windows: dist/win-unpacked/ 或 dist/*.exe
# macOS: dist/mac/ 或 dist/*.dmg
# Linux: dist/linux-unpacked/ 或 dist/*.AppImage
```

## 🔧 技术架构

### 集成方案

1. **Electron 主进程**
   - 启动时自动启动 Express API 服务器
   - 动态分配可用端口（默认从8000开始）
   - 应用关闭时自动停止API服务器

2. **API 服务器**
   - 集成在 `electron/main/server.ts`
   - 提供完整的RESTful API
   - 支持酒店信息管理和用户管理

3. **前端渲染进程**
   - 通过IPC通信获取API服务器端口
   - 动态配置axios baseURL
   - 自动适配Electron环境

### 文件结构

```
├── electron/
│   ├── main/
│   │   ├── index.ts          # Electron主进程
│   │   └── server.ts         # 集成的API服务器
│   └── preload/
│       └── index.ts          # 预加载脚本，暴露API
├── src/
│   ├── utils/
│   │   └── electronApi.ts    # Electron API工具函数
│   └── axios/
│       └── service.ts        # 动态配置axios
└── package.json              # 项目配置
```

## 📋 功能特性

### ✅ 已实现功能

1. **一体化部署**
   - 单个exe文件包含前后端
   - 无需额外安装Node.js或其他依赖
   - 自动端口检测和分配

2. **API服务集成**
   - Express服务器嵌入Electron主进程
   - 支持所有原有API功能
   - 数据持久化（内存存储，可扩展为文件/数据库）

3. **智能配置**
   - 开发环境：使用独立的Node.js服务器
   - 生产环境：使用集成的Electron API服务器
   - 自动检测环境并配置axios

4. **IPC通信**
   - 主进程与渲染进程安全通信
   - 实时获取API服务器状态
   - 支持端口变更通知

## 🔄 环境切换

### Mock模式 → Electron API模式

1. 修改 `.env.base`：
   ```env
   VITE_USE_MOCK=false
   ```

2. 重启应用

### 开发模式 → 生产模式

开发时：
- 前端：Vite开发服务器 (http://localhost:4000)
- 后端：独立Node.js服务器 (http://localhost:8000)

生产时：
- 前端：Electron渲染进程
- 后端：Electron主进程中的Express服务器

## 🛠️ 构建配置

### electron-builder配置

项目使用 `electron-builder.json5` 配置文件：

```json5
{
  "appId": "com.example.hotel-admin",
  "productName": "酒店管理系统",
  "directories": {
    "output": "dist"
  },
  "files": [
    "dist-electron",
    "dist"
  ],
  "extraResources": [
    {
      "from": "server/uploads",
      "to": "uploads",
      "filter": ["**/*"]
    }
  ]
}
```

### 依赖管理

**生产依赖** (打包到exe中):
- express
- cors
- vue
- element-plus
- 等前端依赖

**开发依赖** (仅开发时使用):
- electron
- electron-builder
- vite
- typescript
- 等构建工具

## 🚀 部署流程

### 1. 准备构建

```bash
# 安装依赖
npm install

# 检查TypeScript
npm run ts:check
```

### 2. 构建应用

```bash
# 构建前端资源
npm run build

# 构建Electron应用
npm run build:electron
```

### 3. 测试构建结果

```bash
# 运行打包后的应用
./dist/win-unpacked/酒店管理系统.exe
```

## 🔍 调试和故障排除

### 开发调试

1. **查看Electron控制台**
   ```javascript
   // 在主进程中
   console.log('API Server started on port:', port)
   ```

2. **查看渲染进程控制台**
   - 打开开发者工具 (F12)
   - 查看Network面板的API请求

3. **IPC通信调试**
   ```javascript
   // 在渲染进程中
   const apiStatus = await window.electronAPI.getApiStatus()
   console.log('API Status:', apiStatus)
   ```

### 常见问题

1. **端口冲突**
   - 系统自动检测可用端口
   - 从8000开始递增查找

2. **API请求失败**
   - 检查Electron主进程是否成功启动API服务器
   - 确认axios baseURL是否正确设置

3. **构建失败**
   - 检查所有依赖是否正确安装
   - 确认TypeScript编译无错误

### 性能优化

1. **减小包体积**
   ```bash
   # 分析包大小
   npm run build -- --analyze
   ```

2. **启动速度优化**
   - API服务器异步启动
   - 预加载关键资源

## 📦 分发

### Windows

```bash
# 生成安装包
npm run build:electron

# 输出文件
# dist/酒店管理系统 Setup 1.9.4.exe  (安装包)
# dist/win-unpacked/                  (绿色版)
```

### 自动更新

可以集成 `electron-updater` 实现自动更新：

```bash
npm install electron-updater
```

## 🎉 总结

现在您的Electron应用具备了：

1. ✅ **完全独立运行** - 单个exe包含前后端
2. ✅ **自动端口管理** - 智能避免端口冲突
3. ✅ **开发生产一致** - 相同的API接口
4. ✅ **安全通信** - IPC机制保证安全
5. ✅ **易于部署** - 无需额外配置

您的Vue3 + Node.js + Electron全栈桌面应用已经完成！🎊
