// Electron API 工具函数

declare global {
  interface Window {
    electronAPI?: {
      getApiPort: () => Promise<number>
      getApiStatus: () => Promise<{
        port: number
        url: string
        status: string
      }>
    }
    process?: {
      type?: string
    }
  }
}

// 检查是否在Electron环境中
export const isElectron = (): boolean => {
  return !!(window && window.process && window.process.type)
}

// 获取API服务器端口
export const getApiPort = async (): Promise<number> => {
  if (isElectron() && window.electronAPI) {
    try {
      return await window.electronAPI.getApiPort()
    } catch (error) {
      console.warn('Failed to get API port from Electron:', error)
    }
  }
  // 默认端口
  return 8000
}

// 获取API服务器状态
export const getApiStatus = async () => {
  if (isElectron() && window.electronAPI) {
    try {
      return await window.electronAPI.getApiStatus()
    } catch (error) {
      console.warn('Failed to get API status from Electron:', error)
    }
  }

  // 默认状态
  const port = await getApiPort()
  return {
    port,
    url: `http://localhost:${port}`,
    status: 'unknown'
  }
}

// 获取API基础URL
export const getApiBaseUrl = async (): Promise<string> => {
  const port = await getApiPort()
  return `http://localhost:${port}`
}

// 动态设置axios基础URL
export const setupApiBaseUrl = async () => {
  try {
    const baseURL = await getApiBaseUrl()
    console.log(`🔗 API Base URL: ${baseURL}`)
    return baseURL
  } catch (error) {
    console.error('Failed to setup API base URL:', error)
    return 'http://localhost:8000'
  }
}
