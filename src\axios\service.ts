import axios, { AxiosError } from 'axios'
import { defaultRequestInterceptors, defaultResponseInterceptors } from './config'

import { AxiosInstance, InternalAxiosRequestConfig, RequestConfig, AxiosResponse } from './types'
import { ElMessage } from 'element-plus'
import { REQUEST_TIMEOUT } from '@/constants'
import { getApiBaseUrl } from '@/utils/electronApi'

export const PATH_URL = import.meta.env.VITE_API_BASE_PATH

// 动态设置baseURL
let dynamicBaseURL = PATH_URL

// 初始化时设置API基础URL
const initializeApiBaseUrl = async () => {
  try {
    // 检查是否在Electron环境中且未使用mock
    if (import.meta.env.VITE_USE_MOCK !== 'true') {
      dynamicBaseURL = await getApiBaseUrl()
      console.log('🔗 Dynamic API Base URL:', dynamicBaseURL)
    }
  } catch (error) {
    console.warn('Failed to get dynamic API base URL:', error)
    dynamicBaseURL = PATH_URL
  }
}

// 立即初始化
initializeApiBaseUrl()

const abortControllerMap: Map<string, AbortController> = new Map()

const axiosInstance: AxiosInstance = axios.create({
  timeout: REQUEST_TIMEOUT,
  baseURL: dynamicBaseURL
})

// 导出更新baseURL的函数
export const updateBaseURL = (newBaseURL: string) => {
  dynamicBaseURL = newBaseURL
  axiosInstance.defaults.baseURL = newBaseURL
  console.log('📡 Updated axios baseURL to:', newBaseURL)
}

axiosInstance.interceptors.request.use((res: InternalAxiosRequestConfig) => {
  const controller = new AbortController()
  const url = res.url || ''
  res.signal = controller.signal
  abortControllerMap.set(
    import.meta.env.VITE_USE_MOCK === 'true' ? url.replace('/mock', '') : url,
    controller
  )
  return res
})

axiosInstance.interceptors.response.use(
  (res: AxiosResponse) => {
    const url = res.config.url || ''
    abortControllerMap.delete(url)
    // 这里不能做任何处理，否则后面的 interceptors 拿不到完整的上下文了
    return res
  },
  (error: AxiosError) => {
    console.log('err： ' + error) // for debug
    ElMessage.error(error.message)
    return Promise.reject(error)
  }
)

axiosInstance.interceptors.request.use(defaultRequestInterceptors)
axiosInstance.interceptors.response.use(defaultResponseInterceptors)

const service = {
  request: (config: RequestConfig) => {
    return new Promise(async (resolve, reject) => {
      // 在每次请求前确保baseURL是最新的
      if (import.meta.env.VITE_USE_MOCK !== 'true') {
        try {
          const currentBaseURL = await getApiBaseUrl()
          if (currentBaseURL !== axiosInstance.defaults.baseURL) {
            updateBaseURL(currentBaseURL)
          }
        } catch (error) {
          console.warn('Failed to update baseURL:', error)
        }
      }

      if (config.interceptors?.requestInterceptors) {
        config = config.interceptors.requestInterceptors(config as any)
      }

      axiosInstance
        .request(config)
        .then((res) => {
          resolve(res)
        })
        .catch((err: any) => {
          reject(err)
        })
    })
  },
  cancelRequest: (url: string | string[]) => {
    const urlList = Array.isArray(url) ? url : [url]
    for (const _url of urlList) {
      abortControllerMap.get(_url)?.abort()
      abortControllerMap.delete(_url)
    }
  },
  cancelAllRequest() {
    for (const [_, controller] of abortControllerMap) {
      controller.abort()
    }
    abortControllerMap.clear()
  }
}

export default service
